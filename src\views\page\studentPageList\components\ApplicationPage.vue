<template>
  <div class="application-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>申请管理</h2>
      <p>管理积分申请，查看申请状态和历史记录</p>
    </div>

    <!-- 申请表单区域 -->
    <el-card class="form-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-icon><EditPen /></el-icon>
          <span>积分申请</span>
        </div>
      </template>

      <el-form :model="applicationForm" :rules="applicationRules" ref="applicationFormRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请类型" prop="pointsChange">
              <el-radio-group v-model="applicationForm.pointsChange">
                <el-radio :label="1">加分申请</el-radio>
                <el-radio :label="2">扣分申请</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请分值" prop="points">
              <el-input-number
                v-model="applicationForm.points"
                :min="1"
                :max="applicationForm.pointsChange === 1 ? 100 : 50"
                style="width: 100%"
                placeholder="请输入申请分值"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 学生搜索选择 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="选择学生" prop="selectedStudent">
              <el-select
                v-model="applicationForm.selectedStudent"
                filterable
                remote
                reserve-keyword
                placeholder="请输入学号或姓名搜索学生"
                :remote-method="searchStudentsMethod"
                :loading="studentSearchLoading"
                @change="handleStudentSelect"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="student in studentOptions"
                  :key="student.studentId"
                  :label="`${student.studentNo} - ${student.realName} (${student.className || '未知班级'})`"
                  :value="student.studentId"
                >
                  <span style="float: left">{{ student.studentNo }} - {{ student.realName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ student.className || '未知班级' }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 学生信息显示 -->
        <el-row :gutter="20" v-if="selectedStudentInfo">
          <el-col :span="8">
            <el-form-item label="学号">
              <el-input v-model="selectedStudentInfo.studentNo" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="姓名">
              <el-input v-model="selectedStudentInfo.realName" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="班级">
              <el-input v-model="selectedStudentInfo.className" readonly />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="申请原因" prop="reason">
          <el-input
            v-model="applicationForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请详细描述申请原因"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="证明材料">
          <div class="upload-container">
            <el-upload
              action="#"
              list-type="picture-card"
              :file-list="fileList"
              :auto-upload="false"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              :before-upload="beforeUpload"
              accept="image/*"
              multiple
            >
              <el-icon><Plus /></el-icon>
            </el-upload>

            <div class="upload-tips">
              <p>支持上传图片作为证明材料：</p>
              <ul>
                <li>支持 JPG、PNG、GIF 等图片格式</li>
                <li>单个文件大小不超过 5MB</li>
                <li>可以上传多张图片</li>
              </ul>
            </div>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitApplication" :loading="submitting">
            提交申请
          </el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button @click="loadStudentInfo" :loading="studentInfoLoading">
            获取学生信息
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 批量导入快捷入口 -->
    <el-card class="quick-actions-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-icon><Operation /></el-icon>
          <span>快捷操作</span>
        </div>
      </template>

      <div class="quick-actions">
        <el-button
          type="primary"
          size="large"
          @click="goToBatchImport"
          style="width: 200px; height: 50px;"
        >
          <el-icon><Upload /></el-icon>
          批量导入申请
        </el-button>
        <p style="margin-top: 10px; color: #666; font-size: 14px;">
          通过Excel文件批量导入学生积分申请，提高工作效率
        </p>
      </div>
    </el-card>

    <!-- 申请记录列表 -->
    <el-card class="records-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-icon><Document /></el-icon>
          <span>申请记录</span>
          <div class="header-actions">
            <el-button size="small" @click="refreshApplications">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :inline="true" :model="searchForm" size="small">
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="全部状态" style="width: 120px">
              <el-option label="全部" value="" />
              <el-option label="待审核" value="pending" />
              <el-option label="已通过" value="approved" />
              <el-option label="已拒绝" value="rejected" />
            </el-select>
          </el-form-item>
          <el-form-item label="类型">
            <el-select v-model="searchForm.pointsChange" placeholder="全部类型" style="width: 120px">
              <el-option label="全部" value="" />
              <el-option label="加分" :value="1" />
              <el-option label="扣分" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              style="width: 240px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="searchApplications">查询</el-button>
            <el-button size="small" @click="resetSearchForm">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 申请列表 -->
      <div class="application-list">
        <el-table :data="applicationRecords" stripe v-loading="loading">
          <el-table-column prop="applyId" label="申请编号" width="100" />
          <el-table-column prop="createTime" label="提交时间" width="150">
            <template #default="scope">
              {{ formatDate(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="pointsChange" label="类型" width="80">
            <template #default="scope">
              <el-tag :type="scope.row.pointsChange === 1 ? 'success' : 'danger'" size="small">
                {{ scope.row.pointsChange === 1 ? '加分' : '扣分' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="points" label="分值" width="80">
            <template #default="scope">
              <span :class="scope.row.pointsChange === 1 ? 'points-add' : 'points-deduct'">
                {{ scope.row.pointsChange === 1 ? '+' : '-' }}{{ scope.row.points }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="reason" label="申请原因" min-width="200" show-overflow-tooltip />
          <el-table-column label="审核状态" width="120">
            <template #default="scope">
              <div class="status-column">
                <el-tag :type="getOverallStatusType(scope.row)" size="small">
                  {{ getOverallStatusText(scope.row) }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="scope">
              <el-button
                type="text"
                size="small"
                @click="viewApplicationDetail(scope.row)"
              >
                详情
              </el-button>
              <el-button
                v-if="canCancel(scope.row)"
                type="text"
                size="small"
                @click="cancelApplication(scope.row)"
              >
                撤销
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.pageNum"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handlePageSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </el-card>
    <!-- 申请详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="申请详情" width="700px">
      <div v-if="selectedApplication" class="application-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请编号">{{ selectedApplication.applyId }}</el-descriptions-item>
          <el-descriptions-item label="申请类型">
            <el-tag :type="selectedApplication.pointsChange === 1 ? 'success' : 'danger'" size="small">
              {{ selectedApplication.pointsChange === 1 ? '加分申请' : '扣分申请' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="学号">{{ selectedApplication.studentNo }}</el-descriptions-item>
          <el-descriptions-item label="申请分值">
            <span :class="selectedApplication.pointsChange === 1 ? 'points-add' : 'points-deduct'">
              {{ selectedApplication.pointsChange === 1 ? '+' : '-' }}{{ selectedApplication.points }}分
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="提交时间" :span="2">{{ formatDate(selectedApplication.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="申请原因" :span="2">
            {{ selectedApplication.reason }}
          </el-descriptions-item>

          <!-- 审核状态详情 -->
          <el-descriptions-item label="导员/讲师审核" :span="2">
            <el-tag :type="getApprovalStatusType(selectedApplication.status)" size="small">
              {{ getApprovalStatusLabel(selectedApplication.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="主任审核" :span="2">
            <el-tag :type="getApprovalStatusType(selectedApplication.status1)" size="small">
              {{ getApprovalStatusLabel(selectedApplication.status1) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="院长审核" :span="2">
            <el-tag :type="getApprovalStatusType(selectedApplication.status2)" size="small">
              {{ getApprovalStatusLabel(selectedApplication.status2) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 附件列表 -->
        <div v-if="selectedApplication.img" class="attachments-section">
          <h4>附件列表</h4>
          <div class="attachment-list">
            <div v-for="(url, index) in getAttachmentUrls(selectedApplication.img)" :key="index" class="attachment-item">
              <el-icon><Document /></el-icon>
              <span>附件{{ index + 1 }}</span>
              <el-button type="text" size="small" @click="viewAttachment(url)">查看</el-button>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'

// 定义emit
const emit = defineEmits(['switchToImport'])
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  EditPen, Document, Refresh, Plus, Upload, Operation
} from '@element-plus/icons-vue'
import {
  getStudentApplicationList,
  cancelStudentApplication,
  getStudentInfo,
  uploadApplicationFile
} from '@/api/student/application'
import { addJiFen, searchStudents } from '@/api/system/points'

// 表单引用
const applicationFormRef = ref()

// 状态变量
const submitting = ref(false)
const loading = ref(false)
const studentInfoLoading = ref(false)
const studentSearchLoading = ref(false)

// 移除了Excel导入相关状态，已迁移到BatchImportPage.vue

// 图片上传相关
const fileList = ref([])
const uploadedUrls = ref([])

// 动态获取上传请求头
const getUploadHeaders = () => {
  const token = localStorage.getItem('Authorization')
  console.log('当前token:', token)
  return {
    Authorization: token || ''
  }
}

const uploadHeaders = getUploadHeaders()

// 学生搜索相关
const studentOptions = ref([])
const selectedStudentInfo = ref(null)

// 申请表单
const applicationForm = reactive({
  pointsChange: 1, // 1-加分，2-扣分
  points: null,
  selectedStudent: null, // 选中的学生ID
  studentNo: '',
  classId: null,
  reason: '',
  attachments: [],
  img: '' // 存储上传后的图片URL，多个URL用逗号分隔
})

// 申请表单验证规则
const applicationRules = {
  pointsChange: [{ required: true, message: '请选择申请类型', trigger: 'change' }],
  points: [{ required: true, message: '请输入申请分值', trigger: 'blur' }],
  selectedStudent: [{ required: true, message: '请选择学生', trigger: 'change' }],
  reason: [{ required: true, message: '请输入申请原因', trigger: 'blur' }]
}

// 搜索表单
const searchForm = reactive({
  status: '',
  pointsChange: '',
  dateRange: []
})

// 分页信息
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 申请记录数据
const applicationRecords = ref([])

// 申请详情对话框
const detailDialogVisible = ref(false)
const selectedApplication = ref(null)

// 方法
// 文件选择变化处理
const handleFileChange = async (file, fileList) => {
  console.log('文件选择变化:', file, fileList)

  // 检查文件类型和大小
  if (!beforeUpload(file.raw)) {
    return
  }

  try {
    // 手动上传文件
    const result = await uploadApplicationFile(file.raw)
    console.log('上传结果:', result)

    if (result.status === 200 && result.data && result.data.code === 200) {
      // 上传成功
      uploadedUrls.value.push(result.data.data)
      applicationForm.img = uploadedUrls.value.join(',')
      ElMessage.success('图片上传成功')

      // 更新文件列表状态
      file.status = 'success'
      file.url = result.data.data
    } else {
      ElMessage.error('图片上传失败: ' + (result.data?.message || '未知错误'))
      // 从文件列表中移除失败的文件
      const index = fileList.findIndex(f => f.uid === file.uid)
      if (index > -1) {
        fileList.splice(index, 1)
      }
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('图片上传失败: ' + (error.message || '网络错误'))
    // 从文件列表中移除失败的文件
    const index = fileList.findIndex(f => f.uid === file.uid)
    if (index > -1) {
      fileList.splice(index, 1)
    }
  }
}

// 文件上传前检查
const beforeUpload = (file) => {
  const isValidType = ['image/jpeg', 'image/png', 'image/gif'].includes(file.type)
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isValidType) {
    ElMessage.error('只能上传 JPG/PNG/GIF 格式的图片!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

// 移除图片
const handleFileRemove = (file) => {
  console.log('移除文件:', file)
  // 从uploadedUrls中移除对应的URL
  if (file.url) {
    const urlIndex = uploadedUrls.value.indexOf(file.url)
    if (urlIndex > -1) {
      uploadedUrls.value.splice(urlIndex, 1)
      applicationForm.img = uploadedUrls.value.join(',')
    }
  }
}

// 学生搜索方法
const searchStudentsMethod = async (keyword) => {
  if (!keyword || keyword.trim() === '') {
    studentOptions.value = []
    return
  }

  try {
    studentSearchLoading.value = true
    const response = await searchStudents(keyword)
    console.log('学生搜索结果:', response)

    if (response.status === 200 && response.data && response.data.code === 200) {
      studentOptions.value = response.data.data || []
    } else {
      studentOptions.value = []
      console.warn('学生搜索失败:', response.data?.message || '未知错误')
    }
  } catch (error) {
    console.error('学生搜索失败:', error)
    studentOptions.value = []
  } finally {
    studentSearchLoading.value = false
  }
}

// 处理学生选择
const handleStudentSelect = (studentId) => {
  if (!studentId) {
    selectedStudentInfo.value = null
    applicationForm.studentNo = ''
    applicationForm.classId = null
    return
  }

  const student = studentOptions.value.find(s => s.studentId === studentId)
  if (student) {
    selectedStudentInfo.value = {
      studentNo: student.studentNo,
      realName: student.realName,
      className: student.className || '未知班级'
    }
    applicationForm.studentNo = student.studentNo
    applicationForm.classId = student.classId
    console.log('选中学生信息:', selectedStudentInfo.value)
  }
}



// 提交申请
const submitApplication = async () => {
  try {
    await applicationFormRef.value.validate()

    if (!selectedStudentInfo.value) {
      ElMessage.error('请先选择学生')
      return
    }

    submitting.value = true

    // 构建提交数据，使用与PointsAdd.vue相同的格式
    const submitData = {
      studentNo: applicationForm.studentNo,
      classId: applicationForm.classId,
      pointsChange: applicationForm.pointsChange,
      points: applicationForm.points,
      reason: applicationForm.reason,
      img: applicationForm.img
    }

    console.log('提交申请数据:', submitData)

    const result = await addJiFen(submitData)
    console.log('申请提交结果:', result)

    // 检查响应格式
    if (result && result.data && result.data.code === 200) {
      ElMessage.success('申请提交成功，请等待审核')
      resetForm()
      refreshApplications()
    } else if (result && result.code === 200) {
      ElMessage.success('申请提交成功，请等待审核')
      resetForm()
      refreshApplications()
    } else {
      const errorMsg = (result && result.data && result.data.message) ||
                      (result && result.message) ||
                      '申请提交失败'
      ElMessage.error('申请提交失败: ' + errorMsg)
    }
  } catch (error) {
    console.error('提交申请失败:', error)
    ElMessage.error('申请提交失败')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  applicationFormRef.value?.resetFields()
  applicationForm.attachments = []
  applicationForm.img = ''
  applicationForm.selectedStudent = null
  applicationForm.studentNo = ''
  applicationForm.classId = null
  selectedStudentInfo.value = null
  studentOptions.value = []
  fileList.value = []
  uploadedUrls.value = []
}

// 加载学生信息
const loadStudentInfo = async () => {
  try {
    studentInfoLoading.value = true
    const result = await getStudentInfo()
    if (result.code === 200 && result.data) {
      applicationForm.studentNo = result.data.studentNo || ''
      applicationForm.classId = result.data.classId || null
      ElMessage.success('学生信息加载成功')
    } else {
      ElMessage.warning('无法获取学生信息，请手动填写')
    }
  } catch (error) {
    console.error('获取学生信息失败:', error)
    ElMessage.warning('获取学生信息失败，请手动填写')
  } finally {
    studentInfoLoading.value = false
  }
}

// 刷新申请记录
const refreshApplications = () => {
  loadApplications()
}

// 搜索申请记录
const searchApplications = () => {
  pagination.pageNum = 1
  loadApplications()
}

// 重置搜索表单
const resetSearchForm = () => {
  searchForm.status = ''
  searchForm.pointsChange = ''
  searchForm.dateRange = []
  pagination.pageNum = 1
  loadApplications()
}

// 分页处理
const handlePageSizeChange = (size) => {
  pagination.pageSize = size
  pagination.pageNum = 1
  loadApplications()
}

const handlePageChange = (page) => {
  pagination.pageNum = page
  loadApplications()
}

// 加载申请记录
const loadApplications = async () => {
  try {
    loading.value = true

    const params = {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      ...searchForm
    }

    // 处理时间范围
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startDate = searchForm.dateRange[0]
      params.endDate = searchForm.dateRange[1]
    }

    console.log('加载申请记录参数:', params)
    const result = await getStudentApplicationList(params)
    console.log('申请记录响应:', result)

    if (result && result.status === 200 && result.data) {
      if (result.data.code === 200) {
        // 处理分页数据
        const pageData = result.data.data
        applicationRecords.value = pageData.records || []
        pagination.total = pageData.total || 0
        console.log('加载申请记录成功，数量:', applicationRecords.value.length)
      } else {
        ElMessage.error('加载申请记录失败: ' + (result.data.message || '未知错误'))
      }
    } else {
      ElMessage.error('加载申请记录失败: 响应格式错误')
    }
  } catch (error) {
    console.error('加载申请记录失败:', error)
    ElMessage.error('加载申请记录失败: ' + (error.message || '网络错误'))
  } finally {
    loading.value = false
  }
}

// 查看申请详情
const viewApplicationDetail = (application) => {
  selectedApplication.value = application
  detailDialogVisible.value = true
}

// 撤销申请
const cancelApplication = async (application) => {
  try {
    await ElMessageBox.confirm('确定要撤销这个申请吗？', '确认撤销', {
      type: 'warning'
    })

    const result = await cancelStudentApplication(application.applyId)
    if (result.code === 200) {
      ElMessage.success('申请已撤销')
      refreshApplications()
    } else {
      ElMessage.error('撤销失败: ' + result.message)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('撤销申请失败:', error)
      ElMessage.error('撤销申请失败')
    }
  }
}

// 判断是否可以撤销
const canCancel = (application) => {
  // 只有待审核状态才能撤销
  return application.status === 1 && application.status1 === 1 && application.status2 === 1
}

// 获取整体审核状态类型
const getOverallStatusType = (application) => {
  // 如果任一级别被拒绝，显示为拒绝
  if (application.status === 3 || application.status1 === 3 || application.status2 === 3) {
    return 'danger'
  }
  // 如果所有级别都通过，显示为成功
  if (application.status === 2 && application.status1 === 2 && application.status2 === 2) {
    return 'success'
  }
  // 否则为待审核
  return 'warning'
}

// 获取整体审核状态文本
const getOverallStatusText = (application) => {
  // 如果任一级别被拒绝，显示为拒绝
  if (application.status === 3 || application.status1 === 3 || application.status2 === 3) {
    return '已拒绝'
  }
  // 如果所有级别都通过，显示为通过
  if (application.status === 2 && application.status1 === 2 && application.status2 === 2) {
    return '已通过'
  }
  // 否则为待审核
  return '待审核'
}

// 获取审核状态类型
const getApprovalStatusType = (status) => {
  const statusMap = {
    1: 'warning', // 待审核
    2: 'success', // 通过
    3: 'danger'   // 拒绝
  }
  return statusMap[status] || 'info'
}

// 获取审核状态标签
const getApprovalStatusLabel = (status) => {
  const statusMap = {
    1: '待审核',
    2: '已通过',
    3: '已拒绝'
  }
  return statusMap[status] || '未知'
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

// 获取附件URL列表
const getAttachmentUrls = (imgString) => {
  if (!imgString) return []
  return imgString.split(',').filter(url => url.trim())
}

// 查看附件
const viewAttachment = (url) => {
  window.open(url, '_blank')
}

// Excel导入相关方法已迁移到BatchImportPage.vue

// ================= 快捷操作方法 =================
// 跳转到批量导入页面
const goToBatchImport = () => {
  // 使用emit通知父组件切换到批量导入页面
  emit('switchToImport')
}

// 页面加载时获取申请记录
onMounted(() => {
  loadApplications()
})
</script>

<style scoped>
.application-page {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
  text-align: center;
}

.page-header h2 {
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 28px;
  font-weight: 600;
}

.page-header p {
  color: #7f8c8d;
  font-size: 14px;
  margin: 0;
}

.form-card, .records-card, .import-card, .quick-actions-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: none;
}

.quick-actions {
  text-align: center;
  padding: 20px;
}

.quick-actions .el-button {
  font-size: 16px;
  font-weight: 600;
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
}

.card-header .el-icon {
  margin-right: 8px;
  color: #3498db;
}

.header-actions {
  margin-left: auto;
}

.filter-section {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.application-list {
  margin-top: 20px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

.points-add {
  color: #27ae60;
  font-weight: 600;
}

.points-deduct {
  color: #e74c3c;
  font-weight: 600;
}

.status-column {
  display: flex;
  align-items: center;
}

.application-detail {
  padding: 20px 0;
}

.attachments-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.attachments-section h4 {
  margin-bottom: 12px;
  color: #2c3e50;
  font-size: 16px;
}

.attachment-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.attachment-item .el-icon {
  margin-right: 8px;
  color: #6c757d;
}

.attachment-item span {
  flex: 1;
  color: #495057;
}

.upload-demo {
  width: 100%;
}

/* 学生端主题色彩 */
:deep(.el-button--primary) {
  background-color: #3498db;
  border-color: #3498db;
}

:deep(.el-button--primary:hover) {
  background-color: #2980b9;
  border-color: #2980b9;
}

:deep(.el-radio-group .el-radio.is-checked .el-radio__inner) {
  background-color: #3498db;
  border-color: #3498db;
}

:deep(.el-tag--success) {
  background-color: #d4edda;
  color: #155724;
  border-color: #c3e6cb;
}

:deep(.el-tag--danger) {
  background-color: #f8d7da;
  color: #721c24;
  border-color: #f5c6cb;
}

:deep(.el-tag--warning) {
  background-color: #fff3cd;
  color: #856404;
  border-color: #ffeaa7;
}

:deep(.el-card__header) {
  background-color: #ffffff;
  border-bottom: 1px solid #e9ecef;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #495057;
  font-weight: 600;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #f8f9fa;
}

:deep(.el-pagination) {
  justify-content: center;
}

:deep(.el-pagination .btn-next),
:deep(.el-pagination .btn-prev),
:deep(.el-pagination .el-pager li) {
  background-color: #ffffff;
  border: 1px solid #ddd;
}

:deep(.el-pagination .el-pager li.active) {
  background-color: #3498db;
  color: #ffffff;
  border-color: #3498db;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .application-page {
    padding: 10px;
  }

  .page-header h2 {
    font-size: 24px;
  }

  .filter-section .el-form--inline .el-form-item {
    display: block;
    margin-bottom: 10px;
  }

  .filter-section .el-form--inline .el-form-item .el-form-item__content {
    margin-left: 0 !important;
  }
}
</style>

<style scoped>
.application-page {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.header-actions {
  margin-left: auto;
}

/* 快捷申请卡片 */
.quick-apply-card {
  margin-bottom: 20px;
}

.quick-apply-card :deep(.el-form-item__label) {
  font-weight: 500;
}

/* 申请记录卡片 */
.application-records-card {
  min-height: 600px;
}

.filter-section {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.application-list {
  min-height: 400px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

/* 积分颜色 */
.points-add {
  color: #67c23a;
  font-weight: bold;
}

.points-deduct {
  color: #f56c6c;
  font-weight: bold;
}

/* 申请详情 */
.application-detail {
  padding: 10px 0;
}

.attachments-section {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.attachments-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.attachment-item span {
  flex: 1;
}

/* 图片上传组件样式 */
.upload-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.upload-tips {
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.upload-tips p {
  margin: 0 0 8px 0;
  font-weight: 500;
  color: #495057;
}

.upload-tips ul {
  margin: 0;
  padding-left: 20px;
  color: #6c757d;
  font-size: 13px;
}

.upload-tips li {
  margin-bottom: 4px;
}

:deep(.el-upload--picture-card) {
  width: 100px;
  height: 100px;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 100px;
  height: 100px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-section .el-form {
    display: block;
  }

  .filter-section .el-form-item {
    margin-bottom: 10px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .header-actions {
    margin-left: 0;
  }
}

/* Excel导入相关样式 */
.import-actions {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
}

.file-info {
  margin-top: 15px;
}

.import-result {
  margin-top: 20px;
}

.result-summary {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.error-summary {
  margin-top: 15px;
}

.error-summary ul {
  margin: 10px 0 0 0;
  padding-left: 20px;
}

.error-summary li {
  margin-bottom: 5px;
  color: #e6a23c;
}

.result-table {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

/* 响应式设计 - Excel导入 */
@media (max-width: 768px) {
  .import-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .import-actions .el-button {
    width: 100%;
    margin-left: 0 !important;
    margin-bottom: 10px;
  }
}
</style>
