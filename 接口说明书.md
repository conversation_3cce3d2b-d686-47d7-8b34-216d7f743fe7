# 云计算学院积分管理系统接口说明书

## 1. 接口概述

### 1.1 文档说明
本文档详细描述了云计算学院积分管理系统的所有API接口，包括新增的文书积分申请功能接口。所有接口均采用RESTful设计风格，使用JSON格式进行数据交换。

### 1.2 基础信息
- **基础URL**：`http://localhost:8080/api`
- **认证方式**：JWT Token
- **请求格式**：`application/json`
- **响应格式**：`application/json`

### 1.3 统一响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

### 1.4 状态码说明
- `200`：操作成功
- `400`：请求参数错误
- `401`：未授权访问
- `403`：权限不足
- `404`：资源不存在
- `500`：服务器内部错误

------img-------
*接口架构图*

## 2. 认证授权接口

### 2.1 用户登录
**接口地址**：`POST /auth/login`

**请求参数**：
```json
{
  "username": "string",
  "password": "string"
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "userInfo": {
      "userId": 1,
      "username": "admin",
      "realName": "管理员",
      "roleId": 1,
      "roleName": "超级管理员"
    }
  }
}
```

### 2.2 用户登出
**接口地址**：`POST /auth/logout`

**请求头**：
```
Authorization: Bearer {token}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "登出成功",
  "data": null
}
```

------img-------
*认证流程图*

## 3. 文书积分申请接口（新增）

### 3.1 提交积分申请
**接口地址**：`POST /cultural-secretary-apply/submit`

**权限要求**：文书角色(roleId=8)

**请求参数**：
```json
{
  "studentNo": "2023001001",
  "pointsChange": 1,
  "points": 10,
  "reason": "参加班级活动表现优秀",
  "evidenceImages": "image1.jpg,image2.jpg"
}
```

**参数说明**：
- `studentNo`：学生学号（必填）
- `pointsChange`：变动类型，1-加分，2-减分（必填）
- `points`：积分数值（必填，正整数）
- `reason`：申请理由（必填）
- `evidenceImages`：证明图片URL，多个用逗号分隔（可选）

**响应示例**：
```json
{
  "code": 200,
  "message": "申请提交成功",
  "data": {
    "id": 1,
    "studentNo": "2023001001",
    "status": 1,
    "createTime": "2025-08-07 10:30:00"
  }
}
```

### 3.2 查看我的申请列表
**接口地址**：`POST /cultural-secretary-apply/myApplications`

**权限要求**：文书角色(roleId=8)

**请求参数**：
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "status": 1,
  "pointsChange": 1,
  "startTime": "2025-08-01 00:00:00",
  "endTime": "2025-08-07 23:59:59"
}
```

**参数说明**：
- `pageNum`：页码（可选，默认1）
- `pageSize`：每页大小（可选，默认10）
- `status`：申请状态（可选）
- `pointsChange`：变动类型（可选）
- `startTime`：开始时间（可选）
- `endTime`：结束时间（可选）

**响应示例**：
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "studentNo": "2023001001",
        "pointsChange": 1,
        "points": 10,
        "reason": "参加班级活动表现优秀",
        "status": 1,
        "createTime": "2025-08-07 10:30:00",
        "student": {
          "studentNo": "2023001001",
          "studentName": "张三",
          "className": "云计算2301班"
        },
        "applyUser": {
          "userId": 10,
          "realName": "李文书"
        }
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 3.3 查看待审核申请（秘书）
**接口地址**：`POST /cultural-secretary-apply/pendingApplications`

**权限要求**：秘书角色(roleId=6)

**请求参数**：
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "status": 1,
  "pointsChange": 1,
  "classId": 1,
  "startTime": "2025-08-01 00:00:00",
  "endTime": "2025-08-07 23:59:59"
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "studentNo": "2023001001",
        "pointsChange": 1,
        "points": 10,
        "reason": "参加班级活动表现优秀",
        "evidenceImages": "image1.jpg,image2.jpg",
        "status": 1,
        "createTime": "2025-08-07 10:30:00",
        "student": {
          "studentNo": "2023001001",
          "studentName": "张三",
          "className": "云计算2301班",
          "currentPoints": 85
        },
        "applyUser": {
          "userId": 10,
          "realName": "李文书"
        },
        "eduClass": {
          "classId": 1,
          "className": "云计算2301班"
        }
      }
    ],
    "total": 5,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 3.4 审核通过申请
**接口地址**：`POST /cultural-secretary-apply/approve/{id}`

**权限要求**：秘书角色(roleId=6)

**路径参数**：
- `id`：申请ID

**请求参数**：
```json
{
  "reviewComment": "申请材料齐全，同意加分"
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "审核通过成功",
  "data": {
    "id": 1,
    "status": 2,
    "reviewTime": "2025-08-07 14:30:00",
    "reviewComment": "申请材料齐全，同意加分"
  }
}
```

### 3.5 审核拒绝申请
**接口地址**：`POST /cultural-secretary-apply/reject/{id}`

**权限要求**：秘书角色(roleId=6)

**路径参数**：
- `id`：申请ID

**请求参数**：
```json
{
  "reviewComment": "证明材料不足，拒绝申请"
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "审核拒绝成功",
  "data": {
    "id": 1,
    "status": 3,
    "reviewTime": "2025-08-07 14:30:00",
    "reviewComment": "证明材料不足，拒绝申请"
  }
}
```

### 3.6 撤销申请
**接口地址**：`POST /cultural-secretary-apply/withdraw/{id}`

**权限要求**：申请人本人

**路径参数**：
- `id`：申请ID

**响应示例**：
```json
{
  "code": 200,
  "message": "撤销成功",
  "data": {
    "id": 1,
    "status": 4,
    "updateTime": "2025-08-07 15:00:00"
  }
}
```

### 3.7 获取班级列表
**接口地址**：`GET /cultural-secretary-apply/classes`

**权限要求**：文书角色(roleId=8)

**响应示例**：
```json
{
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "classId": 1,
      "className": "云计算2301班",
      "stageId": 1,
      "stageName": "专业阶段",
      "studentCount": 30
    }
  ]
}
```

### 3.8 获取班级学生列表
**接口地址**：`GET /cultural-secretary-apply/students/{classId}`

**权限要求**：文书角色(roleId=8)

**路径参数**：
- `classId`：班级ID

**响应示例**：
```json
{
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "studentNo": "2023001001",
      "studentName": "张三",
      "gender": 1,
      "phone": "13800138001",
      "points": 85,
      "status": "0",
      "className": "云计算2301班"
    },
    {
      "studentNo": "2023001002",
      "studentName": "李四",
      "gender": 0,
      "phone": "13800138002",
      "points": 92,
      "status": "0",
      "className": "云计算2301班"
    }
  ]
}
```

------img-------
*文书申请流程图*

## 4. 传统积分申请接口

### 4.1 学生提交申请
**接口地址**：`POST /points-apply/studentApply`

**权限要求**：学生角色(roleId=7)

**请求参数**：
```json
{
  "studentNo": "2023001001",
  "pointsChange": 1,
  "points": 5,
  "reason": "参加学院活动获奖",
  "evidenceImages": "award.jpg"
}
```

### 4.2 导员审核接口
**接口地址**：`POST /points-apply/DYJStg`

**权限要求**：导员角色(roleId=5)

**请求参数**：
```
id: 申请ID
```

### 4.3 主任审核接口
**接口地址**：`POST /points-apply/ZRtg`

**权限要求**：主任角色(roleId=2,3)

### 4.4 院长审核接口
**接口地址**：`POST /points-apply/YZtg`

**权限要求**：院长角色(roleId=1)

------img-------
*传统申请审核流程图*

## 5. 学生管理接口

### 5.1 学生列表查询
**接口地址**：`POST /edu-student/list`

**请求参数**：
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "studentName": "张三",
  "className": "云计算2301班",
  "status": "0"
}
```

### 5.2 学生详情查询
**接口地址**：`GET /edu-student/{studentNo}`

### 5.3 学生积分记录
**接口地址**：`POST /points-record/studentRecords`

------img-------
*学生管理模块接口图*

## 6. 统计分析接口

### 6.1 积分统计
**接口地址**：`POST /statistics/pointsStats`

### 6.2 班级排名
**接口地址**：`POST /statistics/classRanking`

### 6.3 个人排名
**接口地址**：`POST /statistics/personalRanking`

------img-------
*统计分析接口图*

## 7. 文件上传接口

### 7.1 图片上传
**接口地址**：`POST /minio/upload`

**请求格式**：`multipart/form-data`

**请求参数**：
- `file`：图片文件

**响应示例**：
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "fileName": "evidence_20250807_143000.jpg",
    "fileUrl": "http://localhost:9000/points-system/evidence_20250807_143000.jpg",
    "fileSize": 1024000
  }
}
```

------img-------
*文件上传流程图*

## 8. 错误码说明

### 8.1 业务错误码
- `40001`：用户名或密码错误
- `40002`：用户已被禁用
- `40003`：权限不足
- `40004`：学生不存在
- `40005`：申请不存在
- `40006`：申请已被处理
- `40007`：只能操作本班学生
- `40008`：学生状态异常

### 8.2 系统错误码
- `50001`：数据库连接失败
- `50002`：文件上传失败
- `50003`：外部服务调用失败

------img-------
*错误处理流程图*

## 9. 接口调用示例

### 9.1 JavaScript调用示例
```javascript
// 文书提交积分申请
const submitApplication = async (data) => {
  try {
    const response = await axios.post('/cultural-secretary-apply/submit', data, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.code === 200) {
      console.log('申请提交成功');
      return response.data.data;
    } else {
      throw new Error(response.data.message);
    }
  } catch (error) {
    console.error('申请提交失败:', error.message);
    throw error;
  }
};

// 秘书审核申请
const approveApplication = async (id, reviewComment) => {
  try {
    const response = await axios.post(`/cultural-secretary-apply/approve/${id}`, {
      reviewComment
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('审核失败:', error.message);
    throw error;
  }
};
```

### 9.2 Java调用示例
```java
// 使用RestTemplate调用接口
@Service
public class ApiService {
    
    @Autowired
    private RestTemplate restTemplate;
    
    public Result submitApplication(CulturalSecretaryApply apply) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(token);
        
        HttpEntity<CulturalSecretaryApply> entity = new HttpEntity<>(apply, headers);
        
        return restTemplate.postForObject(
            "/cultural-secretary-apply/submit", 
            entity, 
            Result.class
        );
    }
}
```

------img-------
*接口调用时序图*

## 10. 接口测试

### 10.1 测试环境
- **测试地址**：`http://localhost:8080/api`
- **测试工具**：Postman、Swagger UI
- **测试数据**：使用测试数据库

### 10.2 测试用例
详细的接口测试用例请参考《测试计划文档》。

------img-------
*接口测试流程图*

## 11. 版本更新记录

### v1.0.0 (2025-08-07)
- 新增文书积分申请功能接口
- 完善权限控制机制
- 优化接口响应格式
- 增加操作日志记录

### v0.9.0 (2025-07-15)
- 基础积分申请接口
- 用户认证授权接口
- 学生管理接口
- 统计分析接口

本接口说明书将随着系统功能的完善持续更新，确保接口文档的准确性和时效性。
