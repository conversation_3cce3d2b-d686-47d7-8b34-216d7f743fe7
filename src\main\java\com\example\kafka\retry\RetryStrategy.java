package com.example.kafka.retry;

/**
 * 重试策略接口
 */
public interface RetryStrategy {
    
    /**
     * 确定是否应该重试消息
     * 
     * @param message 可重试消息
     * @param exception 导致失败的异常
     * @return 如果应该重试返回true，否则返回false
     */
    <T> boolean shouldRetry(RetryableMessage<T> message, Throwable exception);
    
    /**
     * 计算下一次重试的延迟时间（毫秒）
     * 
     * @param message 可重试消息
     * @return 下一次重试前的延迟时间（毫秒）
     */
    <T> long calculateRetryDelay(RetryableMessage<T> message);
    
    /**
     * 获取重试主题名称
     * 
     * @param originalTopic 原始主题
     * @param retryCount 重试次数
     * @return 重试主题名称
     */
    String getRetryTopic(String originalTopic, int retryCount);
    
    /**
     * 获取死信主题名称
     * 
     * @param originalTopic 原始主题
     * @return 死信主题名称
     */
    String getDeadLetterTopic(String originalTopic);
}
