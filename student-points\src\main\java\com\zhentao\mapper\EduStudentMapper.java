package com.zhentao.mapper;

import com.zhentao.pojo.EduStudent;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 学生表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Mapper
public interface EduStudentMapper extends BaseMapper<EduStudent> {

    @Insert("insert into edu_student (student_no,real_name,gender,phone,email,class_id,points,status,create_by,create_time,update_by,update_time,remark,del_flag) " +
            "values(#{student_no},#{real_name},#{gender},#{phone},#{email},#{class_id},#{points},#{status},#{create_by},#{create_time},#{update_by},#{update_time},#{remark},#{del_flag})")
    void add(EduStudent eduStudent);

    @Select("delete from edu_student where id=#{id}")
    void del(Integer id);

    @Select("update edu_student set student_no=#{student_no},real_name=#{real_name},gender=#{gender},phone=#{phone},email=#{email},class_id=#{class_id},points=#{points},status=#{status},create_by=#{create_by},create_time=#{create_time},update_by=#{update_by},update_time=#{update_time},remark=#{remark},del_flag=#{del_flag} where id=#{id}")
    void update(EduStudent eduStudent);
    @Select("select * from edu_student where name like ('%',#{name},'%')")
    List<EduStudent> listname();
    @Select("SELECT s.*, c.class_name as className FROM edu_student s " +
            "LEFT JOIN edu_class c ON s.class_id = c.class_id " +
            "WHERE s.student_no LIKE CONCAT('%', #{keyword}, '%') " +
            "OR s.real_name LIKE CONCAT('%', #{keyword}, '%') " +
            "LIMIT 10")
    List<EduStudent> searchStudents(String keyword);

    @Select("SELECT s.*, c.class_name as className FROM edu_student s " +
            "LEFT JOIN edu_class c ON s.class_id = c.class_id " +
            "LIMIT 10")
    List<EduStudent> searchStudentsLimited();

    @Select("SELECT s.*, c.class_name as className FROM edu_student s " +
            "LEFT JOIN edu_class c ON s.class_id = c.class_id " +
            "WHERE s.class_id = #{classId} " +
            "AND (s.student_no LIKE CONCAT('%', #{keyword}, '%') " +
            "OR s.real_name LIKE CONCAT('%', #{keyword}, '%')) " +
            "LIMIT 10")
    List<EduStudent> searchStudentsByClass(@Param("keyword") String keyword, @Param("classId") Integer classId);

    @Select("SELECT s.*, c.class_name as className FROM edu_student s " +
            "LEFT JOIN edu_class c ON s.class_id = c.class_id " +
            "WHERE (c.teacher_id = #{teacherId} OR c.counselor_id = #{teacherId}) " +
            "AND (s.student_no LIKE CONCAT('%', #{keyword}, '%') " +
            "OR s.real_name LIKE CONCAT('%', #{keyword}, '%')) " +
            "LIMIT 10")
    List<EduStudent> searchStudentsForTeacher(@Param("keyword") String keyword, @Param("teacherId") Integer teacherId);
}