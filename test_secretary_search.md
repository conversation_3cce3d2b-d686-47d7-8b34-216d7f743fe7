# 文书搜索学生功能测试

## 问题描述
文书用户在学生端积分管理页面搜索学生时，能够看到所有学生而不是仅限于本班学生。

## 问题原因
在 `EduStudentServiceImpl.java` 的 `searchStudents` 方法中，文书角色（role_id = 8）没有被包含在权限控制逻辑中，导致文书可以搜索所有学生。

## 修改内容

### 1. 修改权限控制逻辑
在 `searchStudents` 方法中添加了文书角色的权限控制：

```java
} else if (roleIds.contains(8)) {
    // 文书(8)：只能搜索自己班级的学生
    System.out.println("用户具有文书角色，限制只能搜索自己班级的学生，用户ID: " + userId);
    return searchStudentsForSecretary(keyword, userId);
}
```

### 2. 添加文书专用搜索方法
添加了 `searchStudentsForSecretary` 方法，用于限制文书只能搜索自己班级的学生：

```java
private List<EduStudent> searchStudentsForSecretary(String keyword, Integer secretaryUserId) {
    try {
        // Handle empty keyword case
        if (keyword == null || keyword.trim().isEmpty()) {
            keyword = "";
        }
        
        // 获取文书所在的班级ID
        Integer classId = getSecretaryClassId(secretaryUserId);
        if (classId == null) {
            System.out.println("文书用户ID " + secretaryUserId + " 未找到对应的班级信息");
            return new ArrayList<>();
        }
        
        System.out.println("文书用户ID " + secretaryUserId + " 所在班级ID: " + classId);
        // 使用班级ID查询班级内的学生
        return eduStudentMapper.searchStudentsByClass(keyword, classId);
    } catch (Exception e) {
        System.err.println("文书搜索学生失败: " + e.getMessage());
        return new ArrayList<>();
    }
}
```

### 3. 添加获取文书班级ID的方法
添加了 `getSecretaryClassId` 方法，通过文书的用户名（学号）查找对应的学生记录，获取班级ID：

```java
private Integer getSecretaryClassId(Integer secretaryUserId) {
    try {
        // 获取文书用户信息
        SysUser secretary = sysUserMapper.selectById(secretaryUserId);
        if (secretary == null) {
            return null;
        }

        // 通过用户名（假设用户名就是学号）查找学生记录
        LambdaQueryWrapper<EduStudent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EduStudent::getStudentNo, secretary.getUsername());
        wrapper.eq(EduStudent::getDelFlag, "0");
        EduStudent student = eduStudentMapper.selectOne(wrapper);

        return student != null ? student.getClassId() : null;
    } catch (Exception e) {
        System.err.println("获取文书班级ID失败: " + e.getMessage());
        return null;
    }
}
```

## 测试步骤
1. 使用文书账号登录系统
2. 进入学生端积分管理页面
3. 在搜索框中输入学生姓名或学号
4. 验证搜索结果是否只显示本班学生

## 预期结果
文书用户搜索学生时，只能看到自己班级的学生，无法搜索到其他班级的学生。

### 4. 自动创建学生记录功能
为了解决文书用户没有对应学生记录的问题，添加了自动创建功能：

```java
private EduStudent createSecretaryStudentRecord(SysUser secretary) {
    try {
        EduStudent student = new EduStudent();
        student.setStudentNo(secretary.getUsername());
        student.setRealName(secretary.getRealName());
        student.setGender(secretary.getGender());
        student.setPhone(secretary.getPhone());
        student.setEmail(secretary.getEmail());
        student.setClassId(1); // 默认分配到班级ID为1的班级
        student.setPoints(100); // 初始积分100
        student.setStatus(0); // 正常状态
        student.setCreateBy(1L); // 系统创建
        student.setCreateTime(new Date());
        student.setDelFlag(0); // 未删除

        boolean success = save(student);
        if (success) {
            System.out.println("成功为文书用户 " + secretary.getUsername() + " 创建学生记录，班级ID: " + student.getClassId());
            return student;
        } else {
            System.err.println("为文书用户 " + secretary.getUsername() + " 创建学生记录失败");
            return null;
        }
    } catch (Exception e) {
        System.err.println("创建文书学生记录失败: " + e.getMessage());
        return null;
    }
}
```

## 修改文件
- `student-points/src/main/java/com/zhentao/service/impl/EduStudentServiceImpl.java`
- `fix_secretary_student_record.sql` - 手动修复SQL脚本（可选）

## 相关接口
- GET `/edu-student/search?keyword={keyword}` - 学生搜索接口
- 前端调用：`src/api/system/points.js` 中的 `searchStudents` 方法

## 解决方案总结
1. **权限控制修复**：为文书角色添加了专门的权限控制逻辑
2. **自动创建学生记录**：当文书用户没有对应学生记录时，系统会自动创建
3. **班级限制搜索**：文书用户只能搜索自己班级的学生
4. **日志记录**：添加了详细的日志记录，便于调试和监控

现在文书用户在搜索学生时，系统会：
1. 检查文书角色权限
2. 获取文书所在班级ID（如果没有学生记录会自动创建）
3. 只返回同班级的学生搜索结果

## 最新改进（解决"查不到数据"问题）

### 问题分析
从日志可以看出系统已经正确：
- ✅ 识别文书角色
- ✅ 创建学生记录
- ✅ 获取班级ID

但搜索仍然没有结果，原因可能是：
1. 班级中没有其他学生
2. SQL查询缺少必要的过滤条件
3. 搜索关键词不匹配

### 解决方案

#### 1. 改进SQL查询
在 `searchStudentsByClass` 方法中添加了 `del_flag` 和 `status` 过滤：

```sql
SELECT s.*, c.class_name as className FROM edu_student s
LEFT JOIN edu_class c ON s.class_id = c.class_id
WHERE s.class_id = #{classId}
AND s.del_flag = '0'
AND s.status = 0
AND (s.student_no LIKE CONCAT('%', #{keyword}, '%')
OR s.real_name LIKE CONCAT('%', #{keyword}, '%'))
LIMIT 10
```

#### 2. 添加详细调试日志
在搜索方法中添加了详细的调试信息：
- 显示班级中所有学生
- 显示搜索关键词
- 显示搜索结果

#### 3. 自动创建测试数据
当为文书创建学生记录时，自动创建同班级的测试学生：
- 2505001 - 张三
- 2505002 - 李四
- 2505003 - 王五
- 2505004 - 赵六
- 2505005 - 钱七

### 测试步骤
1. 使用文书账号登录
2. 进入学生搜索页面
3. 尝试搜索：
   - 空关键词（应显示所有同班学生）
   - "张" （应找到张三）
   - "2505" （应找到所有2505开头的学号）
   - "其他班" （应该找不到，因为不在同班级）

### 预期结果
- 文书只能看到班级ID为1的学生
- 搜索结果包含详细的调试日志
- 系统会自动创建测试数据便于验证
