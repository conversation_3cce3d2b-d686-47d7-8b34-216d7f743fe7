<template>
  <div class="user-settings-page">
    <div class="settings-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h2><el-icon><Setting /></el-icon> 个人设置</h2>
        <p>管理您的个人信息和账户设置</p>
      </div>

      <!-- 设置内容 -->
      <div class="settings-content">
        <!-- 基本信息卡片 -->
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <el-icon><User /></el-icon>
              <span>基本信息</span>
            </div>
          </template>
          
          <el-form 
            :model="userForm" 
            :rules="userRules" 
            ref="userFormRef" 
            label-width="100px"
            class="user-form"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="姓名" prop="realName">
                  <el-input v-model="userForm.realName" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="学号" prop="studentNo">
                  <el-input v-model="userForm.studentNo" disabled />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="班级" prop="className">
                  <el-input v-model="userForm.className" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="角色" prop="role">
                  <el-input v-model="roleText" disabled />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>

        <!-- 密码修改卡片 -->
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <el-icon><Lock /></el-icon>
              <span>密码修改</span>
            </div>
          </template>
          
          <el-form 
            :model="passwordForm" 
            :rules="passwordRules" 
            ref="passwordFormRef" 
            label-width="100px"
            class="password-form"
          >
            <el-form-item label="当前密码" prop="oldPassword">
              <el-input 
                v-model="passwordForm.oldPassword" 
                type="password" 
                placeholder="请输入当前密码"
                show-password
              />
            </el-form-item>
            
            <el-form-item label="新密码" prop="newPassword">
              <el-input 
                v-model="passwordForm.newPassword" 
                type="password" 
                placeholder="请输入新密码"
                show-password
              />
            </el-form-item>
            
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input 
                v-model="passwordForm.confirmPassword" 
                type="password" 
                placeholder="请再次输入新密码"
                show-password
              />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="handleChangePassword" :loading="passwordLoading">
                修改密码
              </el-button>
              <el-button @click="resetPasswordForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 账户安全卡片 -->
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <el-icon><Key /></el-icon>
              <span>账户安全</span>
            </div>
          </template>
          
          <div class="security-items">
            <div class="security-item">
              <div class="security-info">
                <h4>登录状态</h4>
                <p>当前登录时间：{{ loginTime }}</p>
              </div>
              <el-button type="danger" @click="forceLogout">强制退出</el-button>
            </div>
            
            <div class="security-item">
              <div class="security-info">
                <h4>账户状态</h4>
                <p>账户状态正常</p>
              </div>
              <el-tag type="success">正常</el-tag>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Setting, User, Lock, Key } from '@element-plus/icons-vue'
import request from '@/request'
import router from '@/router/index.js';

// 表单引用
const userFormRef = ref()
const passwordFormRef = ref()

// 用户信息表单
const userForm = reactive({
  realName: '',
  studentNo: '',
  className: '',
  role: 0
})

// 密码修改表单
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 密码修改加载状态
const passwordLoading = ref(false)

// 登录时间
const loginTime = ref('')

// 角色文本
const roleText = computed(() => {
  return userForm.role === 2 ? '学生' : '普通用户'
})

// 表单验证规则
const userRules = {
  realName: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  studentNo: [
    { required: true, message: '请输入学号', trigger: 'blur' }
  ]
}

const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 初始化用户信息
const initUserInfo = () => {
  try {
    const studentInfo = JSON.parse(localStorage.getItem('studentList'))
    if (studentInfo) {
      userForm.realName = studentInfo.realName || ''
      userForm.studentNo = studentInfo.studentNo || ''
      userForm.className = studentInfo.className || ''
      userForm.role = studentInfo.role || 0
    }
  } catch (error) {
    console.error('解析用户信息失败:', error)
  }
}

// 修改密码
const handleChangePassword = async () => {
  try {
    await passwordFormRef.value.validate()
    
    passwordLoading.value = true
    
    // 调用后端API进行密码修改
    const response = await request({
      url: '/sysUser/updateUserPassword',
      method: 'post',
      data: {
        oldPassword: passwordForm.oldPassword,
        newPassword: passwordForm.newPassword,
        confirmPassword: passwordForm.confirmPassword
      }
    })
    console.log( response.data);
    if (response.data.code === 200) {
      ElMessage.success('密码修改成功')
      resetPasswordForm()
      localStorage.clear()
      router.push('/login')
    } else {
      ElMessage.error(response.data.message || '密码修改失败')
    }
    
  } catch (error) {
    ElMessage.error(error.message || '密码修改失败，请重试')
  } finally {
    passwordLoading.value = false
  }
}

// 重置密码表单
const resetPasswordForm = () => {
  passwordForm.oldPassword = ''
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
  passwordFormRef.value?.resetFields()
}

// 强制退出
const forceLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    localStorage.clear()
    window.location.href = '/login'
    
  } catch {
    // 用户取消操作
  }
}

// 格式化当前时间
const formatCurrentTime = () => {
  const now = new Date()
  return now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

onMounted(() => {
  initUserInfo()
  loginTime.value = formatCurrentTime()
})
</script>

<style scoped>
.user-settings-page {
  padding: 20px;
}

.settings-container {
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h2 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: #2c3e50;
  margin-bottom: 10px;
}

.page-header p {
  color: #7f8c8d;
  margin: 0;
}

.settings-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.settings-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #2c3e50;
}

.user-form,
.password-form {
  margin-top: 20px;
}

.security-items {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.security-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.security-info h4 {
  margin: 0 0 5px 0;
  color: #2c3e50;
}

.security-info p {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-settings-page {
    padding: 10px;
  }
  
  .security-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style> 