<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Delete, Download } from '@element-plus/icons-vue'
import { getOperationLogPage, clearAllLogs, exportLogs, batchDeleteLogs } from '@/api/system/log'

// 操作类型选项 (对应后端数字类型)
const operationTypeOptions = [
  { value: 1, label: '登录' },
  { value: 2, label: '登出' },
  { value: 3, label: '创建' },
  { value: 4, label: '更新' },
  { value: 5, label: '删除' },
  { value: 6, label: '查询' },
  { value: 7, label: '导入' },
  { value: 8, label: '导出' },
  { value: 9, label: '授权' },
  { value: 10, label: '审核' },
  { value: 11, label: '其他' },
]

// 模块选项
const moduleOptions = [
  { value: 'system', label: '系统管理' },
  { value: 'user', label: '用户管理' },
  { value: 'role', label: '角色管理' },
  { value: 'student', label: '学生管理' },
  { value: 'class', label: '班级管理' },
  { value: 'points', label: '积分管理' },
  { value: 'approval', label: '审批管理' },
  { value: 'statistics', label: '统计分析' }
]

// 状态选项（1-成功，2-失败，3-待审核）
const statusOptions = [
  { value: 1, label: '成功' },
  { value: 2, label: '失败' },
  { value: 3, label: '待审核' }
]

// 搜索表单
const searchForm = reactive({
  username: '',
  realName: '',
  operationType: '',
  module: '',
  status: '',
  startTime: '',
  endTime: ''
})

// 表格数据
const loading = ref(false)
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 选择的行
const selectedRows = ref([])

// 详情对话框
const detailDialogVisible = ref(false)
const currentLog = ref(null)

// 生命周期钩子
onMounted(() => {
  fetchData()
})

// 方法定义
const fetchData = () => {
  loading.value = true

  // 构建查询参数
  const queryParams = {
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    username: searchForm.username || null,
    realName: searchForm.realName || null,
    operationType: searchForm.operationType || null,
    module: searchForm.module || null,
    status: searchForm.status || null,
    startTime: searchForm.startTime || null,
    endTime: searchForm.endTime || null
  }

  // 调用真实API
  getOperationLogPage(queryParams).then(response => {
    console.log('操作日志查询响应:', response)
    if (response.data && response.data.code === 200) {
      const pageData = response.data.data
      tableData.value = pageData.records || []
      total.value = pageData.total || 0
    } else {
      ElMessage.error(response.data?.message || '查询操作日志失败')
      tableData.value = []
      total.value = 0
    }
  }).catch(error => {
    console.error('查询操作日志失败:', error)
    ElMessage.error('查询操作日志失败')
    tableData.value = []
    total.value = 0
  }).finally(() => {
    loading.value = false
  })
}

const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

const resetSearch = () => {
  searchForm.username = ''
  searchForm.realName = ''
  searchForm.operationType = ''
  searchForm.module = ''
  searchForm.status = ''
  searchForm.startTime = ''
  searchForm.endTime = ''
  currentPage.value = 1
  fetchData()
}

const handleSizeChange = (val) => {
  pageSize.value = val
  fetchData()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchData()
}

const handleRefresh = () => {
  fetchData()
}

const handleClear = () => {
  ElMessageBox.confirm(
    '此操作将永久删除所有操作日志，是否继续？',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 调用清空日志API
    clearAllLogs().then(response => {
      console.log('清空日志响应:', response)
      if (response.data && response.data.code === 200) {
        ElMessage.success(response.data.message || '日志清空成功')
        // 刷新页面数据
        fetchData()
      } else {
        ElMessage.error(response.data?.message || '清空日志失败')
      }
    }).catch(error => {
      console.error('清空日志失败:', error)
      ElMessage.error('清空日志失败')
    })
  }).catch(() => {
    ElMessage.info('已取消清空操作')
  })
}

const handleExport = () => {
  // 构建导出查询参数（使用当前搜索条件）
  const exportParams = {
    username: searchForm.username || null,
    realName: searchForm.realName || null,
    operationType: searchForm.operationType || null,
    module: searchForm.module || null,
    status: searchForm.status || null,
    startTime: searchForm.startTime || null,
    endTime: searchForm.endTime || null
  }

  ElMessage.info('正在导出日志，请稍候...')

  // 调用导出日志API
  exportLogs(exportParams).then(response => {
    console.log('导出日志响应:', response)
    if (response.data && response.data.code === 200) {
      const logs = response.data.data

      // 转换数据为CSV格式
      const csvContent = convertToCSV(logs)

      // 创建下载链接
      const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `操作日志_${formatDate(new Date())}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      ElMessage.success(`成功导出 ${logs.length} 条日志记录`)
    } else {
      ElMessage.error(response.data?.message || '导出日志失败')
    }
  }).catch(error => {
    console.error('导出日志失败:', error)
    ElMessage.error('导出日志失败')
  })
}

const showDetail = (row) => {
  currentLog.value = row
  detailDialogVisible.value = true
}

// 处理表格选择
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 批量删除选中的日志
const handleBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要删除的日志记录')
    return
  }

  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedRows.value.length} 条日志记录吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 提取选中行的ID
    const ids = selectedRows.value.map(row => row.id)

    // 调用批量删除API
    batchDeleteLogs(ids).then(response => {
      console.log('批量删除响应:', response)
      if (response.data && response.data.code === 200) {
        ElMessage.success(response.data.message || `成功删除 ${selectedRows.value.length} 条记录`)
        selectedRows.value = []
        fetchData() // 刷新数据
      } else {
        ElMessage.error(response.data?.message || '批量删除失败')
      }
    }).catch(error => {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    })
  }).catch(() => {
    ElMessage.info('已取消删除操作')
  })
}

const getStatusType = (status) => {
  switch (status) {
    case 1: return 'success'  // 成功
    case 2: return 'danger'   // 失败
    case 3: return 'warning'  // 待审核
    default: return 'info'
  }
}

const getOperationTypeLabel = (type) => {
  const option = operationTypeOptions.find(item => item.value === type)
  return option ? option.label : type
}

const getModuleLabel = (module) => {
  const option = moduleOptions.find(item => item.value === module)
  return option ? option.label : module
}

const getStatusLabel = (status) => {
  const option = statusOptions.find(item => item.value === status)
  return option ? option.label : status
}

// 格式化请求参数
const formatParams = (params) => {
  if (!params) return '-'
  try {
    const obj = JSON.parse(params)
    return JSON.stringify(obj, null, 2)
  } catch (e) {
    return params
  }
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  // 如果是字符串，直接返回
  if (typeof dateTime === 'string') {
    return dateTime.replace('T', ' ')
  }
  // 如果是Date对象，格式化
  if (dateTime instanceof Date) {
    return dateTime.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }
  return dateTime
}

// 专门用于导出的时间格式化函数
const formatDateTimeForExport = (dateTime) => {
  if (!dateTime) return ''

  try {
    let date

    // 处理不同格式的时间
    if (typeof dateTime === 'string') {
      // 如果是字符串，尝试解析
      if (dateTime.includes('T')) {
        // ISO格式：2025-07-23T10:00:00
        date = new Date(dateTime)
      } else if (dateTime.includes('-') && dateTime.includes(' ')) {
        // 标准格式：2025-07-23 10:00:00
        date = new Date(dateTime.replace(' ', 'T'))
      } else {
        // 其他格式，直接返回
        return dateTime
      }
    } else if (dateTime instanceof Date) {
      date = dateTime
    } else {
      return String(dateTime)
    }

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return String(dateTime)
    }

    // 格式化为 YYYY-MM-DD HH:mm:ss
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } catch (error) {
    console.error('时间格式化失败:', error, dateTime)
    return String(dateTime)
  }
}

// 格式化日期（用于文件名）
const formatDate = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}${month}${day}_${hours}${minutes}${seconds}`
}

// 转换数据为CSV格式
const convertToCSV = (data) => {
  if (!data || data.length === 0) {
    return '没有数据可导出'
  }

  // CSV表头
  const headers = [
    '序号', '用户名', '真实姓名', '操作类型', '操作模块',
    '操作描述', 'IP地址', '浏览器', '操作系统', '操作状态', '操作时间'
  ]

  // 转换数据行
  const rows = data.map((item, index) => {
    return [
      index + 1,
      item.username || '',
      item.realName || '',
      getOperationTypeLabel(item.operationType),
      getModuleLabel(item.module),
      item.description || '',
      item.ip || '',
      item.browser || '',
      item.os || '',
      getStatusLabel(item.status),
      formatDateTimeForExport(item.operationTime)
    ].map(field => {
      // 处理包含逗号、引号或换行符的字段
      if (typeof field === 'string' && (field.includes(',') || field.includes('"') || field.includes('\n'))) {
        return `"${field.replace(/"/g, '""')}"`
      }
      return field
    }).join(',')
  })

  // 组合CSV内容
  return [headers.join(','), ...rows].join('\n')
}


</script>

<template>
  <div class="operation-log-container">
    <div class="page-header">
      <h2>操作日志</h2>
      <div class="header-actions">
        <el-button
          type="warning"
          :icon="Delete"
          @click="handleBatchDelete"
          :disabled="selectedRows.length === 0"
        >
          删除选中 ({{ selectedRows.length }})
        </el-button>
        <el-button type="danger" :icon="Delete" @click="handleClear">清空日志</el-button>
        <el-button type="success" :icon="Download" @click="handleExport">导出日志</el-button>
        <el-button type="primary" :icon="Refresh" circle @click="handleRefresh" />
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户名">
          <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable />
        </el-form-item>

        <el-form-item label="真实姓名">
          <el-input v-model="searchForm.realName" placeholder="请输入真实姓名" clearable />
        </el-form-item>
        
        <el-form-item label="操作类型">
          <el-select v-model="searchForm.operationType" placeholder="请选择操作类型" style="width: 150px;" clearable>
            <el-option
              v-for="item in operationTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="操作模块">
          <el-select v-model="searchForm.module" placeholder="请选择操作模块" style="width: 150px;" clearable>
            <el-option
              v-for="item in moduleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" style="width: 150px;" clearable>
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="操作时间">
          <el-date-picker
            v-model="searchForm.startTime"
            type="datetime"
            placeholder="开始时间"
            style="width: 180px"
          />
          <span class="date-separator">至</span>
          <el-date-picker
            v-model="searchForm.endTime"
            type="datetime"
            placeholder="结束时间"
            style="width: 180px"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="username" label="用户名" width="120" />
        <el-table-column prop="realName" label="真实姓名" width="100" />
        <el-table-column prop="operationType" label="操作类型" width="100">
          <template #default="scope">
            {{ getOperationTypeLabel(scope.row.operationType) }}
          </template>
        </el-table-column>
        <el-table-column prop="module" label="操作模块" width="100">
          <template #default="scope">
            {{ getModuleLabel(scope.row.module) }}
          </template>
        </el-table-column>
        <el-table-column prop="description" label="操作描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="ip" label="IP地址" width="120" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusLabel(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="operationTime" label="操作时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.operationTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="80">
          <template #default="scope">
            <el-button type="primary" link @click="showDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:currentPage="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    
    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="日志详情"
      width="700px"
    >
      <el-descriptions :column="2" border v-if="currentLog">
        <el-descriptions-item label="用户名">{{ currentLog.username }}</el-descriptions-item>
        <el-descriptions-item label="真实姓名">{{ currentLog.realName }}</el-descriptions-item>
        <el-descriptions-item label="操作类型">{{ getOperationTypeLabel(currentLog.operationType) }}</el-descriptions-item>
        <el-descriptions-item label="操作模块">{{ getModuleLabel(currentLog.module) }}</el-descriptions-item>
        <el-descriptions-item label="操作状态">
          <el-tag :type="getStatusType(currentLog.status)">
            {{ getStatusLabel(currentLog.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="IP地址">{{ currentLog.ip }}</el-descriptions-item>
        <el-descriptions-item label="浏览器">{{ currentLog.browser }}</el-descriptions-item>
        <el-descriptions-item label="操作系统">{{ currentLog.os }}</el-descriptions-item>
        <el-descriptions-item label="操作时间">{{ currentLog.operationTime }}</el-descriptions-item>
        <el-descriptions-item label="日志ID">{{ currentLog.id }}</el-descriptions-item>
        <el-descriptions-item label="操作描述" :span="2">
          <pre class="params-pre">{{ formatParams(currentLog.description) }}</pre>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<style scoped>
.operation-log-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  display: flex;
  flex-direction: column;
  min-height: 100%;
}

/* 隐藏WebKit浏览器的滚动条 */
.operation-log-container::-webkit-scrollbar {
  display: none;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 22px;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-container, .table-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  overflow: visible;
  transition: all 0.3s;
  animation: fadeIn 0.5s ease-in-out;
}

.search-container:hover, .table-container:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.date-separator {
  margin: 0 5px;
}

.pagination-container {
  margin-top: 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}

.params-pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}



/* 添加动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 页面头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 搜索区域样式 */
.search-container {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

/* 表格容器样式 */
.table-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 分页样式 */
.pagination-container {
  padding: 20px;
  display: flex;
  justify-content: center;
  background: #fafafa;
  border-top: 1px solid #ebeef5;
}

/* 状态标签样式优化 */
.el-tag {
  font-weight: 500;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .filter-form {
    flex-direction: column;
  }

  .filter-form .el-form-item {
    margin-right: 0;
    width: 100%;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
    flex-wrap: wrap;
  }

  .operation-log-container {
    padding: 10px;
  }
}
</style>