-- 云计算学院积分管理系统数据库迁移脚本（简化版）
-- 执行日期：2025-08-07
-- 说明：添加文书申请积分功能相关的数据库结构
-- 注意：不修改现有sys_user表结构，通过学生表关联班级信息

-- =====================================================
-- 1. 不修改sys_user表，通过学生表获取班级信息
-- =====================================================

-- 文书功能说明：
-- 1. 文书用户的username应该对应学生表中的student_no
-- 2. 通过学生表的class_id字段获取文书所在班级
-- 3. 文书只能为同班级学生申请积分

-- =====================================================
-- 2. 创建文书积分申请表
-- =====================================================

-- 检查表是否已存在
DROP TABLE IF EXISTS `cultural_secretary_apply`;

-- 创建文书积分申请表
CREATE TABLE `cultural_secretary_apply` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `student_no` varchar(50) NOT NULL COMMENT '学生学号',
  `class_id` int NOT NULL COMMENT '班级Id',
  `apply_user_id` int NOT NULL COMMENT '申请人Id',
  `points_change` int NOT NULL COMMENT '变动状态（1-加分，2-减分）',
  `points` int NOT NULL COMMENT '分值',
  `reason` text COMMENT '申请理由',
  `evidence_images` text COMMENT '证明图片URL，多个以逗号分隔',
  `status` int DEFAULT '1' COMMENT '审核状态（1-待审核，2-已通过，3-已拒绝，4-已撤销）',
  `reviewer_id` int DEFAULT NULL COMMENT '审核人ID',
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `review_comment` text COMMENT '审核意见',
  `create_by` int DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` int DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` text COMMENT '备注',
  `del_flag` int DEFAULT '1' COMMENT '删除标志（1-存在，2-删除）',
  PRIMARY KEY (`id`),
  KEY `idx_student_no` (`student_no`),
  KEY `idx_class_id` (`class_id`),
  KEY `idx_apply_user_id` (`apply_user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_student_status_time` (`student_no`, `status`, `create_time`),
  KEY `idx_class_status_time` (`class_id`, `status`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文书积分申请表';

-- =====================================================
-- 3. 更新角色表，添加文书角色
-- =====================================================

-- 检查文书角色是否已存在
SELECT * FROM `sys_role` WHERE `role_id` = 8 OR `role_name` = '文书';

-- 如果不存在，则插入文书角色
INSERT IGNORE INTO `sys_role` (`role_id`, `role_name`, `role_key`, `role_sort`, `status`, `create_by`, `create_time`, `remark`, `del_flag`) 
VALUES (8, '文书', 'cultural_secretary', 8, 0, 1, NOW(), '班级文化书记，负责班级积分申请', 0);

-- =====================================================
-- 4. 扩展points_record表，支持文书申请来源
-- =====================================================

-- 检查字段是否已存在
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'points_record' 
  AND COLUMN_NAME IN ('apply_type', 'source_table', 'source_id');

-- 添加新字段
ALTER TABLE `points_record` 
ADD COLUMN `apply_type` int DEFAULT 1 COMMENT '申请类型（1-学生申请，2-文书申请，3-系统调整）' AFTER `operation_type`,
ADD COLUMN `source_table` varchar(50) DEFAULT NULL COMMENT '来源表名' AFTER `apply_type`,
ADD COLUMN `source_id` int DEFAULT NULL COMMENT '来源记录ID' AFTER `source_table`;

-- 添加索引
CREATE INDEX `idx_points_record_apply_type` ON `points_record` (`apply_type`);
CREATE INDEX `idx_points_record_source` ON `points_record` (`source_table`, `source_id`);

-- =====================================================
-- 5. 创建测试数据（可选）
-- =====================================================

-- 创建测试文书用户（username必须是现有学生的student_no）
-- 首先查看现有学生，选择一个作为文书
-- SELECT student_no, student_name, class_id FROM edu_student LIMIT 5;

-- 假设选择学号为'2023001001'的学生作为文书
-- 先确保该学生存在
-- INSERT IGNORE INTO `edu_student` (
--   `student_no`, `student_name`, `gender`, `phone`, `email`, `class_id`,
--   `status`, `create_by`, `create_time`, `del_flag`
-- ) VALUES (
--   '2023001001', '张文书', '1', '13800000001', '<EMAIL>', 1,
--   '0', 1, NOW(), '0'
-- );

-- 创建文书用户（username = student_no）
INSERT IGNORE INTO `sys_user` (
  `username`, `password`, `real_name`, `gender`, `phone`, `email`,
  `user_type`, `status`, `create_by`, `create_time`, `del_flag`
) VALUES (
  '2023001001', '$2a$10$7JB720yubVSOfvVaMWye2.bpe1HpoVMpTGYTP2K4OqHDqukjojvi.', -- 密码: 123456
  '张文书', 1, '13800000001', '<EMAIL>',
  8, 0, 1, NOW(), 0
);

-- 为文书用户分配角色
INSERT IGNORE INTO `sys_user_role` (`user_id`, `role_id`)
SELECT u.`user_id`, 8
FROM `sys_user` u
WHERE u.`username` = '2023001001' AND u.`user_type` = 8;

-- 创建测试秘书用户
INSERT IGNORE INTO `sys_user` (
  `username`, `password`, `real_name`, `gender`, `phone`, `email`,
  `user_type`, `status`, `create_by`, `create_time`, `del_flag`
) VALUES (
  'mishu001', '$2a$10$7JB720yubVSOfvVaMWye2.bpe1HpoVMpTGYTP2K4OqHDqukjojvi.', -- 密码: 123456
  '李秘书', 0, '13800000002', '<EMAIL>',
  6, 0, 1, NOW(), 0
);

-- 为秘书用户分配角色
INSERT IGNORE INTO `sys_user_role` (`user_id`, `role_id`)
SELECT u.`user_id`, 6
FROM `sys_user` u
WHERE u.`username` = 'mishu001' AND u.`user_type` = 6;

-- =====================================================
-- 6. 创建文书测试用户（基于现有学生）
-- =====================================================

-- 注意：文书用户的username必须对应学生表中的student_no
-- 这样才能通过学生表查找到班级信息

-- =====================================================
-- 7. 验证数据完整性
-- =====================================================

-- 检查sys_user表结构
DESCRIBE `sys_user`;

-- 检查cultural_secretary_apply表结构
DESCRIBE `cultural_secretary_apply`;

-- 检查角色数据
SELECT * FROM `sys_role` WHERE `role_id` IN (6, 8);

-- 检查用户角色关联
SELECT u.`username`, u.`real_name`, u.`user_type`, r.`role_name`,
       es.`class_id`, ec.`class_name`
FROM `sys_user` u
LEFT JOIN `sys_user_role` ur ON u.`user_id` = ur.`user_id`
LEFT JOIN `sys_role` r ON ur.`role_id` = r.`role_id`
LEFT JOIN `edu_student` es ON u.`username` = es.`student_no`
LEFT JOIN `edu_class` ec ON es.`class_id` = ec.`class_id`
WHERE u.`user_type` IN (6, 8)
ORDER BY u.`user_type`, u.`username`;

-- =====================================================
-- 8. 权限菜单配置（可选）
-- =====================================================

-- 为文书角色添加菜单权限
-- 这部分需要根据实际的菜单表结构来配置

-- 示例菜单插入（需要根据实际菜单表结构调整）
/*
INSERT IGNORE INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `remark`) 
VALUES 
('文书管理', 0, 4, 'cultural-secretary', 'Layout', 'M', '0', '0', '', 'user', 1, NOW(), '文书管理菜单'),
('积分申请', (SELECT menu_id FROM sys_menu WHERE menu_name = '文书管理'), 1, 'apply', 'cultural-secretary/apply', 'C', '0', '0', 'cultural:apply:add', '#', 1, NOW(), '文书积分申请'),
('我的申请', (SELECT menu_id FROM sys_menu WHERE menu_name = '文书管理'), 2, 'my-applications', 'cultural-secretary/my-applications', 'C', '0', '0', 'cultural:apply:list', '#', 1, NOW(), '查看我的申请');

-- 为文书角色分配菜单权限
INSERT IGNORE INTO `sys_role_menu` (`role_id`, `menu_id`) 
SELECT 8, menu_id FROM `sys_menu` WHERE `menu_name` IN ('文书管理', '积分申请', '我的申请');
*/

-- =====================================================
-- 执行完成提示
-- =====================================================

SELECT '数据库迁移脚本执行完成！' AS message;
SELECT '请重启应用服务器以使更改生效。' AS notice;
