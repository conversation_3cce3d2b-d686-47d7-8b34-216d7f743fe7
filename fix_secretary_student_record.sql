-- 修复文书用户对应的学生记录
-- 为文书用户 sunshanshan 创建对应的学生记录

-- 1. 首先查看现有的班级信息
SELECT class_id, class_name, stage_id FROM edu_class WHERE del_flag = 0 LIMIT 5;

-- 2. 查看现有的学生记录
SELECT student_no, real_name, class_id FROM edu_student WHERE del_flag = '0' LIMIT 5;

-- 3. 查看文书用户信息
SELECT user_id, username, real_name, user_type FROM sys_user WHERE username = 'sunshanshan';

-- 4. 为文书用户创建学生记录
-- 选择班级ID为1的2505A班级
INSERT INTO edu_student (
    student_no,
    real_name,
    gender,
    phone,
    email,
    class_id,
    points,
    status,
    create_by,
    create_time,
    del_flag
) VALUES (
    'sunshanshan',           -- 学号，必须与用户名一致
    '孙杉杉',                -- 真实姓名，与用户表一致
    0,                       -- 性别：0-女，1-男
    '15012345678',          -- 手机号
    '<EMAIL>', -- 邮箱
    1,                       -- 班级ID：1对应2505A班级
    100,                     -- 初始积分
    0,                       -- 状态：0-正常
    1,                       -- 创建者ID
    NOW(),                   -- 创建时间
    '0'                      -- 删除标志：0-存在
) ON DUPLICATE KEY UPDATE
    real_name = '孙杉杉',
    class_id = 1,
    del_flag = '0';

-- 4.1 为了测试，再创建几个同班级的学生
INSERT INTO edu_student (
    student_no,
    real_name,
    gender,
    phone,
    email,
    class_id,
    points,
    status,
    create_by,
    create_time,
    del_flag
) VALUES
('2505001', '张三', 1, '13800000001', '<EMAIL>', 1, 95, 0, 1, NOW(), '0'),
('2505002', '李四', 0, '13800000002', '<EMAIL>', 1, 105, 0, 1, NOW(), '0'),
('2505003', '王五', 1, '13800000003', '<EMAIL>', 1, 88, 0, 1, NOW(), '0')
ON DUPLICATE KEY UPDATE
    del_flag = '0';

-- 5. 验证创建结果
SELECT 
    u.username, 
    u.real_name as user_real_name, 
    u.user_type,
    es.student_no, 
    es.real_name as student_real_name,
    es.class_id, 
    ec.class_name
FROM sys_user u
LEFT JOIN edu_student es ON u.username = es.student_no
LEFT JOIN edu_class ec ON es.class_id = ec.class_id
WHERE u.username = 'sunshanshan';

-- 6. 查看该班级的其他学生（用于测试搜索功能）
SELECT student_no, real_name, points 
FROM edu_student 
WHERE class_id = (
    SELECT class_id 
    FROM edu_student 
    WHERE student_no = 'sunshanshan'
) 
AND del_flag = '0'
ORDER BY student_no;
