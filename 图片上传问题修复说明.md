# ApplicationPage.vue 图片上传问题修复说明

## 问题描述
用户在ApplicationPage.vue中上传图片时出现"请先登录"错误，导致无法正常上传证明材料。

## 问题分析

### 1. 认证头配置错误
- **问题**: 使用错误的token key `'token'`
- **实际**: 项目中使用的是`'Authorization'`
- **影响**: 后端无法识别认证信息

### 2. 上传方式问题  
- **问题**: 使用el-upload自动上传，难以控制错误处理
- **影响**: 认证失败时无法提供详细错误信息

### 3. API路径不匹配
- **问题**: API使用`/minio/upload`，后端实际是`/minio/uploadFile`
- **影响**: 请求发送到错误的接口

## 修复方案

### 1. 修复认证头配置

```javascript
// 修复前
const uploadHeaders = ref({
  'Authorization': localStorage.getItem('token') || ''
})

// 修复后
const getUploadHeaders = () => {
  const token = localStorage.getItem('Authorization')
  console.log('当前token:', token)
  return {
    Authorization: token || ''
  }
}
```

### 2. 改为手动上传

```vue
<!-- 修复前：自动上传 -->
<el-upload
  action="/api/minio/uploadFile"
  :headers="uploadHeaders"
  :on-success="handleUploadSuccess"
  :on-error="handleUploadError"
>

<!-- 修复后：手动上传 -->
<el-upload
  action="#"
  :auto-upload="false"
  :on-change="handleFileChange"
>
```

### 3. 实现手动上传逻辑

```javascript
const handleFileChange = async (file, fileList) => {
  // 文件验证
  if (!beforeUpload(file.raw)) {
    return
  }
  
  try {
    // 手动上传
    const result = await uploadApplicationFile(file.raw)
    
    if (result.status === 200 && result.data && result.data.code === 200) {
      // 成功处理
      uploadedUrls.value.push(result.data.data)
      applicationForm.img = uploadedUrls.value.join(',')
      ElMessage.success('图片上传成功')
      
      // 更新文件状态
      file.status = 'success'
      file.url = result.data.data
    } else {
      // 失败处理
      ElMessage.error('图片上传失败: ' + (result.data?.message || '未知错误'))
      // 从列表移除失败文件
      const index = fileList.findIndex(f => f.uid === file.uid)
      if (index > -1) {
        fileList.splice(index, 1)
      }
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('图片上传失败: ' + (error.message || '网络错误'))
  }
}
```

### 4. 修复API路径

```javascript
// src/api/student/application.js
export function uploadApplicationFile(file) {
    const formData = new FormData()
    formData.append('file', file)
    
    return Request({
        url: '/minio/uploadFile', // 修复：使用正确的路径
        method: 'post',
        data: formData,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}
```

## 修复后的功能特性

### ✅ 认证功能
- 正确获取和使用Authorization token
- 自动从localStorage获取最新token
- 添加调试日志便于问题排查

### ✅ 上传功能
- 文件选择后立即自动上传
- 详细的错误处理和用户反馈
- 上传失败时自动清理文件列表
- 支持多张图片上传

### ✅ 用户体验
- 实时上传状态反馈
- 图片预览功能
- 可以移除已上传的图片
- 友好的错误提示信息

### ✅ 数据管理
- 正确维护uploadedUrls数组
- 自动更新applicationForm.img字段
- 图片URL用逗号分隔存储

## 技术改进

1. **错误处理**: 添加详细的console.log用于调试
2. **状态管理**: 正确维护文件列表和URL数组的同步
3. **文件验证**: 保持原有的文件类型和大小检查
4. **API兼容**: 使用项目现有的Request拦截器自动处理认证

## 测试建议

1. **登录状态**: 确保用户已正确登录并有有效token
2. **文件格式**: 测试JPG、PNG、GIF格式图片上传
3. **文件大小**: 测试5MB以内的图片上传
4. **多文件**: 测试同时上传多张图片
5. **移除功能**: 测试移除已上传图片的功能

## 注意事项

1. 确保后端`/minio/uploadFile`接口正常运行
2. 检查MinIO服务是否可访问
3. 确认用户有上传文件的权限
4. 监控浏览器控制台的调试信息

修复完成后，图片上传功能应该可以正常工作，不会再出现"请先登录"的错误。
