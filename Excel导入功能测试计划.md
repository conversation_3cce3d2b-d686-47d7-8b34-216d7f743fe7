# Excel导入功能测试计划

## 测试环境准备

### 1. 后端服务
- 确保Spring Boot应用正常运行
- 确保数据库连接正常
- 确保有测试用的学生和班级数据

### 2. 前端应用
- 确保Vue应用正常运行
- 确保用户已登录且有相应权限
- 确保ApplicationPage页面可以正常访问

### 3. 测试数据
- 准备有效的学生学号、姓名、班级信息
- 准备无效的测试数据用于验证错误处理

## 测试用例

### 测试用例1: 模板下载功能
**目标**: 验证Excel模板可以正常下载

**步骤**:
1. 访问ApplicationPage页面
2. 找到"批量导入申请"卡片
3. 点击"下载导入模板"按钮
4. 检查文件是否成功下载

**预期结果**:
- 文件名为"积分申请导入模板.xlsx"
- 文件包含正确的表头：学号、姓名、班级、申请类型、分值、申请原因
- 包含示例数据行

### 测试用例2: 有效数据导入
**目标**: 验证有效数据可以正常导入和提交

**测试数据**:
```
学号        姓名    班级            申请类型    分值    申请原因
2023001001  张三    计算机2023-1班   加分       5      参加校级比赛获得三等奖
2023001002  李四    计算机2023-1班   减分       3      违反宿舍管理规定
```

**步骤**:
1. 下载模板并填入测试数据
2. 点击"选择Excel文件"上传文件
3. 点击"开始导入"
4. 检查导入结果
5. 点击"批量提交"
6. 检查申请记录列表

**预期结果**:
- 导入成功，显示2条成功记录
- 所有记录验证状态为"验证通过"
- 批量提交成功
- 申请记录列表中出现新的记录

### 测试用例3: 无效数据验证
**目标**: 验证数据验证功能正常工作

**测试数据**:
```
学号        姓名    班级            申请类型    分值    申请原因
9999999999  不存在  不存在班级       加分       5      测试不存在学号
2023001001  错误姓名 计算机2023-1班   加分       5      测试姓名不匹配
2023001001  张三    计算机2023-1班   错误类型    5      测试申请类型错误
2023001001  张三    计算机2023-1班   加分       200     测试分值超出范围
2023001001  张三    计算机2023-1班   加分       5      
```

**步骤**:
1. 下载模板并填入测试数据
2. 上传文件并导入
3. 检查验证结果

**预期结果**:
- 显示相应的错误信息：
  - "学号不存在"
  - "姓名与学号不匹配"
  - "申请类型必须是'加分'或'减分'"
  - "加分分值必须在1-100之间"
  - "申请原因不能为空"

### 测试用例4: 文件格式验证
**目标**: 验证文件格式限制

**步骤**:
1. 尝试上传非Excel文件（如.txt, .pdf）
2. 尝试上传超大文件（>10MB）
3. 尝试上传空文件

**预期结果**:
- 显示相应的错误提示
- 不允许上传不支持的格式

### 测试用例5: 混合数据处理
**目标**: 验证部分有效、部分无效数据的处理

**测试数据**: 包含有效和无效记录的混合数据

**步骤**:
1. 上传混合数据文件
2. 检查导入结果
3. 尝试批量提交

**预期结果**:
- 正确统计成功和失败记录数
- 只提交验证通过的记录
- 显示详细的错误信息

### 测试用例6: 权限验证
**目标**: 验证不同角色的权限限制

**步骤**:
1. 使用秘书角色登录测试
2. 使用其他角色登录测试
3. 尝试为不同范围的学生申请积分

**预期结果**:
- 秘书可以为所有学生申请
- 其他角色按权限限制

### 测试用例7: 大批量数据处理
**目标**: 验证系统处理大量数据的能力

**测试数据**: 100-1000条记录

**步骤**:
1. 准备大量测试数据
2. 上传并导入
3. 检查性能和稳定性

**预期结果**:
- 系统能正常处理大量数据
- 响应时间在可接受范围内
- 内存使用正常

## 错误处理测试

### 网络错误
- 断网情况下的处理
- 服务器错误的处理
- 超时情况的处理

### 数据库错误
- 数据库连接失败
- 数据插入失败
- 事务回滚验证

### 并发测试
- 多用户同时导入
- 同时操作同一学生数据

## 性能测试

### 响应时间
- 模板下载时间 < 5秒
- 文件上传时间 < 10秒
- 数据验证时间 < 30秒
- 批量提交时间 < 60秒

### 资源使用
- 内存使用合理
- CPU使用率正常
- 数据库连接数正常

## 兼容性测试

### 浏览器兼容性
- Chrome
- Firefox
- Safari
- Edge

### Excel版本兼容性
- Excel 2003 (.xls)
- Excel 2007+ (.xlsx)
- WPS表格
- LibreOffice Calc

## 测试报告模板

### 测试结果记录
- [ ] 测试用例1: 模板下载功能
- [ ] 测试用例2: 有效数据导入
- [ ] 测试用例3: 无效数据验证
- [ ] 测试用例4: 文件格式验证
- [ ] 测试用例5: 混合数据处理
- [ ] 测试用例6: 权限验证
- [ ] 测试用例7: 大批量数据处理

### 发现的问题
1. 问题描述
2. 重现步骤
3. 预期结果
4. 实际结果
5. 严重程度
6. 解决方案

### 测试总结
- 功能完整性
- 性能表现
- 用户体验
- 改进建议
