# 云计算学院积分管理系统概要设计说明书

## 1. 引言

### 1.1 编写目的
本文档旨在为云计算学院积分管理系统提供概要设计说明，明确系统的总体架构、功能模块划分、技术选型和数据库设计等关键内容，为详细设计和系统实现提供指导。

### 1.2 项目背景
云计算学院成立于2023年6月，随着学生数量增加和学院规模扩大，传统的Excel表格积分统计方式已无法满足管理需求。为保障每个学生快速、高薪、稳定就业，构建一套完整的积分管理系统势在必行。

### 1.3 系统目标
- 实现积分管理的"规范化、自动化、透明化、数据化"
- 提升学院管理效率，减少人工错误
- 为学院决策提供数据支持
- 激发学生学习积极性，促进全面发展

------img-------
*系统目标架构图*

## 2. 系统总体设计

### 2.1 系统架构
采用前后端分离的B/S架构模式：
- **前端**：Vue.js 3 + Element Plus + Vite
- **后端**：Spring Boot 3 + MyBatis Plus + Spring Security
- **数据库**：MySQL 8.0
- **文件存储**：MinIO对象存储
- **认证授权**：JWT Token

------img-------
*系统总体架构图*

### 2.2 技术架构层次
```
┌─────────────────────────────────────┐
│           表现层 (Presentation)      │
│    Vue.js + Element Plus + Axios    │
├─────────────────────────────────────┤
│            业务层 (Business)         │
│      Spring Boot + Spring MVC       │
├─────────────────────────────────────┤
│           持久层 (Persistence)       │
│        MyBatis Plus + MySQL         │
├─────────────────────────────────────┤
│            基础设施层 (Infrastructure) │
│    Spring Security + JWT + MinIO    │
└─────────────────────────────────────┘
```

### 2.3 系统部署架构
- **Web服务器**：Nginx（负载均衡、静态资源）
- **应用服务器**：Spring Boot内置Tomcat
- **数据库服务器**：MySQL主从复制
- **文件服务器**：MinIO集群
- **缓存服务器**：Redis（可选）

------img-------
*系统部署架构图*

## 3. 功能模块设计

### 3.1 用户权限管理模块
#### 3.1.1 角色体系
- **管理员(1)**：系统超级管理员
- **专业主任(2)**：专业课程管理
- **专高主任(3)**：专高课程管理  
- **讲师(4)**：班级教学管理
- **导员(5)**：学生日常管理
- **秘书(6)**：积分审核管理
- **学生(7)**：积分查询申请
- **文书(8)**：班级积分申请

#### 3.1.2 权限控制
- 基于RBAC模型的权限控制
- 菜单权限、操作权限、数据权限三级控制
- JWT Token无状态认证

------img-------
*用户角色权限关系图*

### 3.2 积分申请管理模块
#### 3.2.1 申请流程
```
学生/文书申请 → 导员/讲师审核 → 专业/专高主任审核 → 院长审核 → 积分生效
```

#### 3.2.2 文书申请功能（新增）
- **申请权限**：文书只能为本班学生申请积分
- **审核流程**：文书申请 → 秘书部审核 → 积分生效
- **申请类型**：加分、扣分
- **证明材料**：支持多图片上传

#### 3.2.3 申请状态管理
- **待审核(1)**：等待相关人员审核
- **已通过(2)**：审核通过，积分已生效
- **已拒绝(3)**：审核拒绝，说明原因
- **已撤销(4)**：申请人主动撤销

------img-------
*积分申请流程图*

### 3.3 积分记录管理模块
#### 3.3.1 积分变动记录
- 记录每次积分变动的详细信息
- 包含变动前后积分、操作人、操作时间、变动原因
- 支持积分变动历史查询和统计

#### 3.3.2 操作日志记录
- 记录所有关键操作的日志信息
- 包含操作人、操作时间、操作类型、操作结果
- 支持日志查询和审计

### 3.4 数据统计分析模块
#### 3.4.1 学生积分统计
- 个人积分排名
- 班级积分排名
- 阶段积分统计
- 积分变动趋势分析

#### 3.4.2 数据可视化
- 使用ECharts进行数据可视化
- 支持柱状图、折线图、饼图等多种图表
- 支持数据导出功能

------img-------
*数据统计分析界面*

## 4. 数据库设计

### 4.1 核心数据表
#### 4.1.1 用户相关表
- `sys_user`：系统用户表
- `sys_role`：角色表
- `sys_user_role`：用户角色关联表
- `sys_menu`：菜单表
- `sys_role_menu`：角色菜单关联表

#### 4.1.2 教育管理表
- `edu_stage`：教育阶段表
- `edu_class`：班级表
- `edu_student`：学生表

#### 4.1.3 积分管理表
- `points_apply`：积分申请表
- `cultural_secretary_apply`：文书积分申请表（新增）
- `points_record`：积分记录表
- `integral_rules`：积分规则表

#### 4.1.4 系统管理表
- `operation_log`：操作日志表
- `activity`：活动表
- `activity_application`：活动申请表

### 4.2 新增文书申请表设计
```sql
CREATE TABLE `cultural_secretary_apply` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `student_no` varchar(50) NOT NULL COMMENT '学生学号',
  `class_id` int NOT NULL COMMENT '班级Id',
  `apply_user_id` int NOT NULL COMMENT '申请人Id',
  `points_change` int NOT NULL COMMENT '变动状态（1-加分，2-减分）',
  `points` int NOT NULL COMMENT '分值',
  `reason` text COMMENT '申请理由',
  `evidence_images` text COMMENT '证明图片URL，多个以逗号分隔',
  `status` int DEFAULT '1' COMMENT '审核状态（1-待审核，2-已通过，3-已拒绝，4-已撤销）',
  `reviewer_id` int DEFAULT NULL COMMENT '审核人ID',
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `review_comment` text COMMENT '审核意见',
  `create_by` int DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` int DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` text COMMENT '备注',
  `del_flag` int DEFAULT '1' COMMENT '删除标志（1-存在，2-删除）',
  PRIMARY KEY (`id`),
  KEY `idx_student_no` (`student_no`),
  KEY `idx_class_id` (`class_id`),
  KEY `idx_apply_user_id` (`apply_user_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文书积分申请表';
```

------img-------
*数据库ER关系图*

## 5. 接口设计

### 5.1 文书申请相关接口
- `POST /cultural-secretary-apply/submit`：提交积分申请
- `POST /cultural-secretary-apply/myApplications`：查看我的申请
- `POST /cultural-secretary-apply/pendingApplications`：查看待审核申请
- `POST /cultural-secretary-apply/approve/{id}`：审核通过
- `POST /cultural-secretary-apply/reject/{id}`：审核拒绝
- `POST /cultural-secretary-apply/withdraw/{id}`：撤销申请
- `GET /cultural-secretary-apply/classes`：获取班级列表
- `GET /cultural-secretary-apply/students/{classId}`：获取学生列表

### 5.2 接口规范
- 统一使用RESTful API设计风格
- 统一返回格式：`{code: 200, message: "success", data: {}}`
- 统一异常处理和错误码定义
- 接口文档使用Swagger自动生成

------img-------
*接口调用时序图*

## 6. 安全设计

### 6.1 认证授权
- JWT Token认证，支持Token刷新
- 基于Spring Security的安全框架
- 密码加密存储（BCrypt）

### 6.2 数据安全
- SQL注入防护
- XSS攻击防护
- CSRF攻击防护
- 敏感数据加密存储

### 6.3 操作审计
- 完整的操作日志记录
- 关键操作的审计追踪
- 数据变更历史记录

------img-------
*系统安全架构图*

## 7. 性能设计

### 7.1 数据库优化
- 合理的索引设计
- 分页查询优化
- 慢查询监控

### 7.2 缓存策略
- Redis缓存热点数据
- 前端组件缓存
- 静态资源CDN加速

### 7.3 并发处理
- 数据库连接池配置
- 线程池配置优化
- 分布式锁处理并发

## 8. 扩展性设计

### 8.1 模块化设计
- 松耦合的模块设计
- 插件化的功能扩展
- 微服务架构预留

### 8.2 配置化管理
- 积分规则配置化
- 审核流程配置化
- 系统参数配置化

------img-------
*系统扩展性架构图*

## 9. 总结

本概要设计说明书详细阐述了云计算学院积分管理系统的总体架构、功能模块、数据库设计和技术实现方案。特别是新增的文书申请积分功能，完善了积分申请的多元化渠道，提高了系统的实用性和灵活性。

系统采用现代化的技术栈，具备良好的可扩展性、安全性和性能，能够满足学院当前和未来的积分管理需求，为学院的数字化转型提供有力支撑。
