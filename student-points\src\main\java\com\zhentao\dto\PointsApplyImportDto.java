package com.zhentao.dto;

import lombok.Data;

/**
 * 积分申请导入DTO
 */
@Data
public class PointsApplyImportDto {
    
    /**
     * 行号（用于错误定位）
     */
    private Integer rowIndex;
    
    /**
     * 学生学号
     */
    private String studentNo;
    
    /**
     * 学生姓名
     */
    private String studentName;
    
    /**
     * 班级名称
     */
    private String className;
    
    /**
     * 班级ID（验证后填充）
     */
    private Integer classId;
    
    /**
     * 申请类型文本（"加分"/"减分"）
     */
    private String pointsChangeText;
    
    /**
     * 申请类型（1-加分，2-减分）
     */
    private Integer pointsChange;
    
    /**
     * 分值
     */
    private Integer points;
    
    /**
     * 申请原因
     */
    private String reason;

    /**
     * 证明素材说明
     */
    private String evidenceMaterial;

    /**
     * 是否验证通过
     */
    private Boolean isValid = true;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 验证后的学生真实姓名（用于核对）
     */
    private String realStudentName;
}
