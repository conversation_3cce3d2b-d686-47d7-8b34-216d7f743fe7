package com.zhentao.dto;

import lombok.Data;
import java.util.List;
import java.util.ArrayList;

/**
 * 导入结果DTO
 */
@Data
public class ImportResultDto {
    
    /**
     * 总记录数
     */
    private Integer totalCount = 0;
    
    /**
     * 成功记录数
     */
    private Integer successCount = 0;
    
    /**
     * 失败记录数
     */
    private Integer failCount = 0;
    
    /**
     * 详细结果列表
     */
    private List<PointsApplyImportDto> details = new ArrayList<>();
    
    /**
     * 错误汇总
     */
    private List<String> errorSummary = new ArrayList<>();
    
    /**
     * 是否有错误
     */
    private Boolean hasError = false;
    
    /**
     * 处理耗时（毫秒）
     */
    private Long processTime;
    
    /**
     * 添加错误汇总
     */
    public void addErrorSummary(String error) {
        if (errorSummary == null) {
            errorSummary = new ArrayList<>();
        }
        errorSummary.add(error);
        hasError = true;
    }
    
    /**
     * 计算统计信息
     */
    public void calculateStats() {
        if (details != null) {
            totalCount = details.size();
            successCount = (int) details.stream().filter(d -> d.getIsValid()).count();
            failCount = totalCount - successCount;
            hasError = failCount > 0;
        }
    }
}
