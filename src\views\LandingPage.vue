<template>
  <div class="landing-page">
    <!-- 顶部导航栏 -->
    <header class="header">
      <div class="logo-container">
        <img src="/images/logo.png" alt="振涛教育" class="logo" />
        <b>云计算学院积分管理系统</b>
      </div>
      <div class="nav-links">
        <a href="#about">关于学院</a>
        <a href="#features">教学特色</a>
        <a href="#career">就业前景</a>
        <a href="#awards">荣誉成果</a>
        <a @click.prevent="goToPointsRules" style="cursor: pointer">积分规则</a>
        <el-button type="primary" class="login-btn" @click="goToLogin">
          <el-icon class="login-icon">
            <User />
          </el-icon>
          <span class="login-text">欢迎访问</span>
          <div class="login-shine"></div>
        </el-button>
      </div>
    </header>

    <!-- 走马灯 -->
    <div class="carousel-section">
      <el-carousel :interval="5000" type="card" height="500px" indicator-position="outside">
        <el-carousel-item v-for="(item, index) in carouselItems" :key="index">
          <div class="carousel-content" :style="{ backgroundImage: `url(${item.image})` }">
            <div class="carousel-overlay">
              <h2>{{ item.title }}</h2>
              <p>{{ item.description }}</p>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>

    <!-- 关于学院 -->
    <section id="about" class="section about-section">
      <!-- 积分消息 -->
      <div class="score-notice-carousel">
        <el-carousel
          :interval="3000"
          arrow="never"
          indicator-position="none"
          height="48px"
          class="score-carousel"
          direction="vertical"
          :autoplay="true"
        >
          <el-carousel-item v-for="(msg, idx) in scoreMessages" :key="idx">
            <div class="score-notice-item" :class="msg.pointsChange">
              <el-icon class="notice-icon" style="color: #409eff; margin-right: 8px">
                <!--  判断加分还是减分,如果是加分显示<Promotion /> 否则显示<WarningFilled /> -->
                <el-icon v-if="msg.pointsChange === 1">
                  <Promotion />
                </el-icon>
                <el-icon v-else>
                  <WarningFilled />
                </el-icon>
              </el-icon>
              <span class="notice-content1" :class="msg.pointsChange === 1 ? 'add' : 'sub'"
              >
                {{ msg.className }}
                {{ msg.studentName }}{{":"}}
                &nbsp;{{ msg.reason }}
              </span>
              <span class="notice-score" :class="msg.pointsChange === 1 ? 'add' : 'sub'">
                {{ msg.pointsChange === 1 ? '+' : '-' }}
                {{
                  msg.pointsChange === 2
                    ? msg.pointsBefore - msg.pointsAfter
                    : msg.pointsAfter - msg.pointsBefore
                }}
              </span>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
      <!-- 排行榜区域 -->
      <h2 class="section-title">云计算积分排行榜</h2>
      <div class="rankings-section">
        <el-row gutter="50" style="width: 100%; justify-content: center; ">
          <el-col :xs="24" :sm="8">
            <el-card class="ranking-card">
              <div class="ranking-header">
                <h3 class="ranking-title">{{ rankingTitle }}</h3>
                <el-button
                  type="primary"
                  size="small"
                  @click="toggleRanking"
                  class="toggle-ranking-btn"
                >
                  {{ toggleButtonText }}
                </el-button>
              </div>
              <ol class="ranking-list">
                <li v-for="(stu, idx) in currentStudents" :key="stu.id">
                  <span class="rank-index">{{ idx + 1 }}</span>
                  <span class="rank-name">{{stu.className}}&nbsp;{{ stu.realName }}</span>
                  <span class="rank-score" :class="rankingScoreClass">{{ stu.points }}分</span>
                </li>
              </ol>
              <!-- 消息轮播 -->
              <div class="score-marquee" :class="marqueeClass">
                <el-carousel
                  :interval="2500"
                  arrow="never"
                  indicator-position="none"
                  height="36px"
                  direction="vertical"
                  :autoplay="true"
                  class="score-marquee"
                  :class="marqueeType"
                >
                  <el-carousel-item v-for="(msg, idx) in currentMessages" :key="idx">
                    <span class="marquee-icon" :class="marqueeIconClass">{{ marqueeIcon }}</span>
                    <div v-if="currentMessages.length === 0">加载中...</div>
                    <span class="marquee-content" :class="marqueeContentClass"
                    >{{ msg.studentName }} {{ marqueeActionText }} {{ msg.points }}分，{{ msg.reason }}</span
                    >
                  </el-carousel-item>
                </el-carousel>
              </div>
            </el-card>
          </el-col>
          <!-- 违纪通报卡片 -->
          <el-col :xs="24" :sm="10">
            <el-card style="max-width: 920px; height: 600px">
              <h3 class="ranking-title" style="color: #f42b2b">违纪通报</h3>
                              <el-carousel
                  height="450px"
                  direction="vertical"
                  type="card"
                  :autoplay="true"
                  :interval="3000"
                  indicator-position="none"
                  :pause-on-hover="true"
                  ref="violationCarousel"
                >
                <el-carousel-item v-for="(item, index) in deductScoreMessages" :key="item">
                  <div class="violation-card-content" @click="handleCardClick(index)" @mouseenter="handleCardHover(index, item, $event)" @mouseleave="handleCardLeave">
                    <div class="violation-image-container">
                      <img
                        :src="item.evidenceImages"
                        class="violation-image"
                        @error="handleImageError"
                      />
                    </div>
                    <div class="violation-text-container">
                      <div class="violation-info">
                        <div class="violation-person">
                          <div class="person-label">违纪人：</div>
                          <div class="person-details">
                            <div class="class-name">{{ item.className }}</div>
                            <div class="student-name">{{ item.studentName }}</div>
                          </div>
                        </div>
                        <div class="violation-points">
                          <div class="points-label">扣除积分：</div>
                          <div class="points-value">-{{ item.points }}</div>
                        </div>

                      </div>
                    </div>
                  </div>
                </el-carousel-item>
              </el-carousel>
            </el-card>
          </el-col>
        </el-row>
      </div>


      <div class="container">
        <h2 class="section-title">关于振涛教育云计算学院</h2>
        <div class="section-content">
          <div class="text-content">
            <p>
              振涛教育云计算学院是国内领先的专业云计算技术培训机构，致力于培养高端云计算人才，为学生提供系统化、专业化的云计算技术培训。学院拥有一支经验丰富的教师团队，采用"理论+实践"的教学模式，确保学生能够掌握云计算领域的前沿技术和实用技能。
            </p>
            <p>
              云计算专业作为培养云计算和大数据相关岗位的专业人才而成立的技术专业。以Java为基础，以云计算和大数据技术为核心，结合AI大模型，打造高端技术产业，培养"云+AI"时代高端技术人才。在企业级应用开发领域，Java为当之无愧的王者编程语言，选择振涛云计算，人生注定不平凡。
            </p>
            <div class="stats">
              <div class="stat-item">
                <div class="stat-number">100%</div>
                <div class="stat-label">就业率</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">500+</div>
                <div class="stat-label">学院学员</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">100%</div>
                <div class="stat-label">AI教学</div>
              </div>
            </div>
          </div>
          <div class="image-content">
            <img src="/images/zhentao/yuna.jpg" alt="云计算奋斗" class="about-image" />
          </div>
        </div>
      </div>
    </section>

    <!-- 教学特色 -->
    <section id="features" class="section features-section">
      <div class="container">
        <h2 class="section-title">教学特色</h2>
        <div class="features-grid">
          <div class="feature-card">
            <el-icon class="feature-icon">
              <Monitor />
            </el-icon>
            <h3>前沿技术课程</h3>
            <p>
              以java编程语言为主，通过在校学习，掌握数据库、Java全栈、分布式、微服务、百万级数据存储与处理等专业知识内容，通过企业级项目开发掌握应用技能，增强动手开发能力，从0到1实现大型项目开发能力。AI助力编程，让学习更简单。
            </p>
          </div>
          <div class="feature-card">
            <el-icon class="feature-icon">
              <Connection />
            </el-icon>
            <h3>实战项目驱动</h3>
            <p>
              通过项目的实战，全程授以学生思维模型培养课程，让学生不仅掌握专业技能，同时能收获为人处世之道，面对问题，不退缩迎难而上，培养学生快速解决问题的能力。
            </p>
          </div>
          <div class="feature-card">
            <el-icon class="feature-icon">
              <User />
            </el-icon>
            <h3>师资力量</h3>
            <p>
              项目经理团队和就业指导团队都来自互联网等大厂。着眼行业未来，规划部署"云+AI"所需，让学生站在科技前沿。讲师团队更是具有"双师型"资格且有丰富的教学和项目经验。强大师资力量保障，助力学生优质就业。
            </p>
          </div>
          <div class="feature-card">
            <el-icon class="feature-icon">
              <Promotion />
            </el-icon>
            <h3>就业保障</h3>
            <p>
              自成立以来，云计算目前已有多个平均薪资优秀班级，部分优异学生达到了高质量就业。云计算就业形势一片大好，选择云计算专业，必将铸就辉煌人生！
            </p>
          </div>
          <div class="feature-card">
            <el-icon class="feature-icon">
              <Cpu />
            </el-icon>
            <h3>AI伴学</h3>
            <p>
              通过 AI 伴学，学员相当于拥有 "永不离线的技术导师 + 专属学习管家"，将技术学习的 "不确定性" 转化为 "可量化的成长路径"，从容应对 Java
              全栈、分布式架构等复杂知识的学习挑战，真正实现 "效率与深度双突破"。
            </p>
          </div>
          <div class="feature-card">
            <el-icon class="feature-icon">
              <Medal />
            </el-icon>
            <h3>学院院长</h3>
            <p>
              云计算专业院长郭溪溪以引领新时代、塑造新格局、精锐聚强师、硬核赢未来四方面内容讲述AI浪潮下云计算专业改革征途。
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- 就业前景 -->
    <section id="career" class="section career-section">
      <div class="container">
        <h2 class="section-title">高薪就业前景</h2>
        <div class="section-content">
          <div class="image-content">
            <div class="career-image-container">
              <div class="career-image-wrapper">
                <img src="/images/zhentao/yunjy.png" alt="云计算就业" class="career-image" />
              </div>
            </div>
          </div>
          <div class="text-content">
            <p class="highlight-text">
              云计算行业正处于蓬勃发展阶段，人才需求量大，薪资水平高。我们的毕业生平均起薪达到18K+，就业率高达100%。踏入云计算领域，就像闯入一片待垦的数字新大陆。在这里，我们解码分布式系统的奥秘，驯服海量数据的洪流，让算力像空气般触手可及。每攻克一个技术难题，每实现一次高效部署，都是在拓宽科技的深度与广度。云计算人以码为犁、以智为种，耕耘出属于未来的科技粮仓，这份对技术的执着，永远滚烫
              。
            </p>
            <div class="career-paths">
              <div class="career-path">
                <h3>云平台架构师</h3>
                <p>平均薪资: 25K-50K</p>
              </div>
              <div class="career-path">
                <h3>云原生开发工程师</h3>
                <p>平均薪资: 18K-35K</p>
              </div>
              <div class="career-path">
                <h3>DevOps工程师</h3>
                <p>平均薪资: 20K-40K</p>
              </div>
              <div class="career-path">
                <h3>Java+AI</h3>
                <p>平均薪资: 18K-45K</p>
              </div>
            </div>
            <div class="companies">
              <p class="companies-title">岗位培养方向:</p>
              <p>
                Java开发工程师、云计算开发工程师、架构师、项目经理、大数据开发工程师、高级数据分析师等。
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 荣誉成果 -->
    <section id="awards" class="section awards-section">
      <div class="container">
        <h2 class="section-title">荣誉成果</h2>
        <div class="section-content">
          <div class="text-content">
            <p>
              振涛教育云计算学院凭借优质的教学质量和出色的就业成果，获得了多项荣誉和认可。我们的学生在各类技能大赛中屡获佳绩，展现了扎实的专业功底和创新能力。
            </p>
            <ul class="awards-list">
              <li>云计算连续三年获得"中国IT教育机构50强"称号</li>
              <li>云计算荣获"最具就业竞争力IT培训机构"奖项</li>
              <li>学生团队在学校"一面二就"大赛多次拿下第一名</li>
              <li>被评为"云计算人才培养示范基地"</li>
              <li>未来已来，决胜AI，全面实现AI化教育</li>
            </ul>
          </div>
          <div class="image-content">
            <img src="/images/zhentao/yunJL.jpg" alt="云计算奖项" class="awards-image" />
          </div>
        </div>
      </div>
    </section>

    <!-- 底部信息 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-logo">
            <img src="/images/logo.png" alt="振涛教育" class="footer-logo-img" />
            <p>振涛教育云计算学院</p>
          </div>
          <div class="footer-links">
            <div class="footer-column">
              <h3>关于我们</h3>
              <a @click.prevent="goToPage('/about')">学院简介</a>
              <a @click.prevent="goToPage('/teachers')">师资力量</a>
              <a @click.prevent="goToPage('/history')">发展历程</a>
              <a @click.prevent="goToPage('/news')">新闻动态</a>
            </div>
            <div class="footer-column">
              <h3>振涛教育微信公众号</h3>
              <el-image src="/images/img.png" style="width: 50%" />
            </div>
            <div class="footer-column">
              <h3>振涛教育官方微信号</h3>
              <el-image src="/images/img_1.png" style="width: 50%" />
            </div>
          </div>
        </div>
        <div class="copyright">
          <p>© 2025 振涛教育云计算学院 版权所有</p>
        </div>
      </div>
    </footer>

    <!-- 右侧悬浮快捷入口（小三角悬浮展开） -->
    <div class="side-toolbar-wrapper" @mouseenter="openToolbar" @mouseleave="closeToolbar">
      <transition name="fade-slide">
        <div v-if="toolbarOpen" class="side-toolbar">
          <div class="toolbar-item" @click="goToHotActivity">
            <el-icon>
              <Promotion />
            </el-icon>
            <span>跳转微信</span>
          </div>
          <div class="toolbar-item" @click="goToVideoCenter">
            <el-icon>
              <VideoCamera />
            </el-icon>
            <span>跳转抖音</span>
          </div>
          <div class="toolbar-item" @click="goToHonorCenter">
            <el-icon>
              <Trophy />
            </el-icon>
            <span>荣誉中心</span>
          </div>
          <div class="toolbar-item" @click="contactDialogVisible = true">
            <el-icon>
              <User />
            </el-icon>
            <span>联系我们</span>
          </div>
        </div>
      </transition>
      <div class="side-triangle" :class="{ open: toolbarOpen }"></div>
    </div>
    <el-dialog v-model="contactDialogVisible" title="联系我们" width="350px" append-to-body>
      <div style="text-align: center">
        <p>电话: 133-4312-2949</p>
        <p>邮箱: <EMAIL></p>
        <p>地址: 河北省保定市振涛教育</p>
      </div>
    </el-dialog>



    <!-- 悬停提示框 -->
    <transition name="tooltip-fade">
      <div 
        v-if="hoveredItem" 
        class="violation-tooltip"
        :style="tooltipStyle"
      >
        <div class="tooltip-content">
          <div class="tooltip-header">
            <span class="tooltip-title">扣分原因</span>
          </div>
          <div class="tooltip-body">
            <div class="tooltip-info">
              <div class="tooltip-person">
                <span class="tooltip-label">违纪人：</span>
                <span class="tooltip-value">{{ hoveredItem.className }} {{ hoveredItem.studentName }}</span>
              </div>
              <div class="tooltip-reason">
                <span class="tooltip-label">扣分原因：</span>
                <div class="tooltip-reason-text">{{ hoveredItem.reason }}</div>
              </div>
            </div>
          </div>
          <div class="tooltip-arrow"></div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
  import { onUnmounted, ref, computed } from 'vue';
  import { useRouter } from 'vue-router';
  import {
    Monitor,
    Connection,
    User,
    Promotion,
    Cpu,
    Medal,
    VideoCamera,
    Trophy,
    WarningFilled
  } from '@element-plus/icons-vue';
  import axios from 'axios';
  import { queryBottomTenStudents, queryTopTenStudents } from '@/api/system/student.js';
  import { queryTodayMinusPoints, queryTodayAddPoints } from '@/api/system/points.js';

  const router = useRouter();

  // 学生加减分消息通知数据
  const scoreMessages = ref();

  const scoreMessagesRef = () => {
    axios({
      url: '/api/points-record/findAll',
      method: 'post'
    }).then((res) => {
      if (res.status == 200) {
        scoreMessages.value = res.data;
      }
    });
  };
  scoreMessagesRef();

  // 走马灯数据
  const carouselItems = [
    {
      image: '/images/zhentao/yun1.png',
      title: '云计算技术培训领导者',
      description: '专注云计算人才培养，打造IT精英'
    },
    {
      image: '/images/zhentao/yun2.png',
      title: '实战驱动，项目引领',
      description: '真实项目实训，无缝对接企业需求'
    },
    {
      image: '/images/zhentao/yun3.png',
      title: '高薪就业保障',
      description: '100%就业率，平均薪资18K+'
    }
  ];
  const intervalIds = ref([]);
  // 排行榜数据和状态管理
  const topStudents = ref([]);
  const bottomStudents = ref([]);
  const showTopRanking = ref(true); // true显示前十名，false显示后十名

  // 计算属性
  const currentStudents = computed(() => {
    return showTopRanking.value ? topStudents.value : bottomStudents.value;
  });

  const rankingTitle = computed(() => {
    return showTopRanking.value ? '学生积分前10名' : '学生积分后10名';
  });

  const toggleButtonText = computed(() => {
    return showTopRanking.value ? '查看后十名' : '查看前十名';
  });

  const rankingScoreClass = computed(() => {
    return showTopRanking.value ? 'rank-score-green' : 'rank-score-red';
  });

  const marqueeClass = computed(() => {
    return showTopRanking.value ? 'score-marquee-green' : 'score-marquee-red';
  });

  const marqueeType = computed(() => {
    return showTopRanking.value ? 'add' : 'subtract';
  });

  const marqueeIcon = computed(() => {
    return showTopRanking.value ? '⬆️' : '⬇️';
  });

  const marqueeIconClass = computed(() => {
    return showTopRanking.value ? 'add-icon' : 'subtract-icon';
  });

  const marqueeContentClass = computed(() => {
    return showTopRanking.value ? 'add-content' : 'subtract-content';
  });

  const marqueeActionText = computed(() => {
    return showTopRanking.value ? '获得加分' : '被扣除';
  });

  const currentMessages = computed(() => {
    return showTopRanking.value ? addScoreMessages.value : deductScoreMessages.value;
  });

  // 切换排名显示
  const toggleRanking = () => {
    showTopRanking.value = !showTopRanking.value;
  };

  // 获取前十名学生数据
  const findTopStudents = () => {
    queryTopTenStudents().then((res) => {
      topStudents.value = res.data.data;
    });
    const id = setInterval(() => {
      queryTopTenStudents().then((res) => {
        topStudents.value = res.data.data;
      });
    }, 5000);
    intervalIds.value.push(id);
  };

  // 获取后十名学生数据
  const findBottomStudents = () => {
    queryBottomTenStudents().then((res) => {
      bottomStudents.value = res.data.data;
    });
    const id = setInterval(() => {
      queryBottomTenStudents().then((res) => {
        bottomStudents.value = res.data.data;
      });
    }, 5000);
    intervalIds.value.push(id);
  };

  findTopStudents();
  findBottomStudents();
  // 加分消息
  const addScoreMessages = ref([]);
  const findAddScoreMessages = () => {
    queryTodayAddPoints().then((res) => {
      addScoreMessages.value = res.data.data;
    });
    const id = setInterval(() => {
      queryTodayAddPoints().then((res) => {
        addScoreMessages.value = res.data.data;
      });
    }, 5000);
    intervalIds.value.push(id);
  };
  findAddScoreMessages();

  // 扣分消息
  const deductScoreMessages = ref([]);
  const findDeductScoreMessages = () => {
    queryTodayMinusPoints().then((res) => {
      // 统一字段名
      deductScoreMessages.value = res.data.data;
      console.log('获取到的违纪数据:', deductScoreMessages.value);
    });
  };
  findDeductScoreMessages();

  onUnmounted(() => {
    intervalIds.value.forEach(id => clearInterval(id));
    intervalIds.value = []; // 清空ID数组
    
    // 清理悬停定时器
    if (hoverTimer.value) {
      clearTimeout(hoverTimer.value);
    }
  });
  // 跳转到登录页
  const goToLogin = () => {
    let item = JSON.parse(localStorage.getItem("roleId"));
    if (item==null){
      router.push('/login');
    }
    if(item==6||item==7){
      router.push('/student/portal');
    }else {
      router.push('/dashboard');
    }
  };

  const goToPointsRules = () => {
    router.push('/points-rules');
  };
  const contactDialogVisible = ref(false);
  const goToHotActivity = () => {
    router.push('/activity-signup');
  };
  const goToVideoCenter = () => {
    window.open(
      'https://www.douyin.com/user/MS4wLjABAAAAftw_WbVjIXErZwyh7L5vfhtXHcI1sPE6rbiwZLL95O_8tzW47jEsaaEM2KQY0Bdd?from_tab_name=main'
    );
  };
  const goToHonorCenter = () => {
    router.push('/awards');
  };
  let toolbarOpen = ref(false);
  let closeTimer = null;

  function openToolbar() {
    if (closeTimer) {
      clearTimeout(closeTimer);
      closeTimer = null;
    }
    toolbarOpen.value = true;
  }

  function closeToolbar() {
    closeTimer = setTimeout(() => {
      toolbarOpen.value = false;
    }, 300);
  }

  const goToPage = (path) => {
    router.push(path);
  };

  // 图片错误处理
  const handleImageError = (event) => {
    event.target.style.display = 'none';
    // 可以设置一个默认图片
    // event.target.src = '/images/default-violation.png';
  };

  // 轮播图引用
  const violationCarousel = ref(null);
  


  // 鼠标悬停处理
  const handleCarouselHover = () => {
    // 鼠标悬停时暂停自动播放
    if (violationCarousel.value) {
      violationCarousel.value.pause();
    }
  };

  const handleCarouselLeave = () => {
    // 鼠标离开时恢复自动播放
    if (violationCarousel.value) {
      violationCarousel.value.start();
    }
  };



  // 点击卡片切换
  const handleCardClick = (index) => {
    if (violationCarousel.value) {
      violationCarousel.value.setActiveItem(index);
    }
  };





  // 悬停提示框
  const hoveredItem = ref(null);
  const tooltipStyle = ref({});
  const hoverTimer = ref(null);

  const handleCardHover = (index, item, event) => {
    // 清除之前的定时器
    if (hoverTimer.value) {
      clearTimeout(hoverTimer.value);
    }
    
    // 设置延迟显示
    hoverTimer.value = setTimeout(() => {
      hoveredItem.value = item;
      
      // 计算提示框位置
      const rect = event.target.getBoundingClientRect();
      const tooltipWidth = 320; // 提示框宽度
      const tooltipHeight = 150; // 预估提示框高度
      
      let left = rect.left + (rect.width / 2) - (tooltipWidth / 2);
      let top = rect.top - tooltipHeight - 15; // 在卡片上方15px
      
      // 确保提示框不超出屏幕边界
      if (left < 10) left = 10;
      if (left + tooltipWidth > window.innerWidth - 10) {
        left = window.innerWidth - tooltipWidth - 10;
      }
      if (top < 10) {
        // 如果上方空间不够，显示在下方
        top = rect.bottom + 15;
      }
      
      tooltipStyle.value = {
        position: 'fixed',
        top: `${top}px`,
        left: `${left}px`,
        zIndex: 10000
      };
      
      // 暂停轮播
      if (violationCarousel.value) {
        violationCarousel.value.pause();
      }
    }, 300); // 300ms延迟
  };

  const handleCardLeave = () => {
    // 清除定时器
    if (hoverTimer.value) {
      clearTimeout(hoverTimer.value);
      hoverTimer.value = null;
    }
    
    hoveredItem.value = null;
    tooltipStyle.value = {};
    
    // 恢复轮播
    if (violationCarousel.value) {
      violationCarousel.value.start();
    }
  };


</script>

<style scoped>
  .score-notice-carousel {
    width: 100%;
    max-width: 800px;
    margin: 32px auto 0 auto;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    padding: 0 16px;
  }

  .score-carousel {
    width: 100%;
    height: 48px;
    line-height: 48px;
  }

  .score-notice-item {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #333;
    height: 48px;
    justify-content: space-between;
  }

  .notice-icon {
    font-size: 20px;
    flex-shrink: 0;
  }

  .notice-content {
    flex: 1;
    margin-left: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .notice-content1 {
    flex: 1;
    margin-left: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .notice-content1.add {
    color: #67c23a;
    font-weight: bold;
  }

  .notice-content1.sub {
    color: #f56c6c;
    font-weight: bold;
  }

  .notice-score {
    font-size: 15px;
    font-weight: 500;
    margin-left: 12px;
    padding: 2px 10px;
    border-radius: 12px;
    background: #f5f7fa;
    min-width: 40px;
    text-align: center;
    transition: background 0.2s;
  }

  .notice-score.add {
    color: #67c23a;
    background: #e9f9ec;
  }

  .notice-score.sub {
    color: #f56c6c;
    background: #fef0f0;
  }

  /* 全局样式 */
  .landing-page {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    color: #333;
    line-height: 1.6;
    background-color: #f9fcff;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .section {
    padding: 80px 0;
  }

  .section-title {
    text-align: center;
    font-size: 36px;
    color: #1a4f8c;
    margin-bottom: 50px;
    position: relative;
  }

  .section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #1a4f8c, #3c9af0);
    border-radius: 2px;
  }

  .section-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 40px;
  }

  /* 头部导航 */
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 30px 12px 30px;
    background: linear-gradient(90deg, #f9fcff 60%, #eaf3ff 100%);
    box-shadow: 0 4px 18px rgba(60, 154, 240, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    border-bottom: 1.5px solid #e6eaf0;
  }

  .logo-container {
    font-size: 28px;
    color: #1a4f8c;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .logo {
    height: 100px;
    transition: transform 0.2s;
  }

  .logo:hover {
    transform: scale(1.07) rotate(-2deg);
  }

  .nav-links {
    display: flex;
    align-items: center;
    gap: 44px;
  }

  .nav-links a {
    text-decoration: none;
    color: #1a4f8c;
    font-size: 18px;
    font-weight: 500;
    transition: all 0.25s;
    padding: 4px 10px;
    border-radius: 8px;
  }

  .nav-links a:hover {
    background: linear-gradient(90deg, #eaf3ff 60%, #d0e6ff 100%);
    color: #1989fa;
  }

  .login-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    font-weight: 600;
    border-radius: 25px;
    padding: 12px 32px;
    font-size: 16px;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    margin-left: 18px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 140px;
    justify-content: center;
  }

  .login-btn:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
    transform: translateY(-3px) scale(1.05);
  }

  .login-btn:active {
    transform: translateY(-1px) scale(1.02);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.35);
  }

  .login-icon {
    font-size: 18px;
    transition: transform 0.3s ease;
  }

  .login-btn:hover .login-icon {
    transform: rotate(360deg);
  }

  .login-text {
    font-weight: 600;
    letter-spacing: 0.5px;
  }

  .login-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
  }

  .login-btn:hover .login-shine {
    left: 100%;
  }

  /* 添加脉冲动画 */
  @keyframes pulse {
    0% {
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }
    50% {
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
    }
    100% {
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }
  }

  .login-btn {
    animation: pulse 2s infinite;
  }

  .login-btn:hover {
    animation: none;
  }

  /* 走马灯部分 */
  .carousel-section {
    margin-top: 90px;
    padding: 0;
  }

  .carousel-content {
    height: 100%;
    background-size: cover;
    background-position: center;
    position: relative;
    border-radius: 8px;
    overflow: hidden;
  }

  .carousel-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 30px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
    color: #fff;
  }

  .carousel-overlay h2 {
    font-size: 32px;
    margin-bottom: 10px;
  }

  .carousel-overlay p {
    font-size: 18px;
    opacity: 0.9;
  }

  /* 关于学院部分 */
  .about-section {
    background-color: #fff;
  }

  .about-section .text-content {
    flex: 1;
  }

  .about-section p {
    margin-bottom: 20px;
    font-size: 16px;
    line-height: 1.8;
  }

  .about-image {
    width: 100%;
    max-width: 500px;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }

  .stats {
    display: flex;
    justify-content: space-between;
    margin-top: 40px;
  }

  .stat-item {
    text-align: center;
  }

  .stat-number {
    font-size: 36px;
    font-weight: bold;
    color: #1a4f8c;
    margin-bottom: 5px;
  }

  .stat-label {
    font-size: 16px;
    color: #666;
  }

  /* 教学特色部分 */
  .features-section {
    background-color: #f0f7ff;
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
  }

  .feature-card {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
    text-align: center;
  }

  .feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(60, 154, 240, 0.1);
  }

  .feature-icon {
    font-size: 40px;
    color: #3c9af0;
    margin-bottom: 20px;
  }

  .feature-card h3 {
    font-size: 20px;
    margin-bottom: 15px;
    color: #1a4f8c;
  }

  .feature-card p {
    font-size: 15px;
    color: #666;
  }

  /* 就业前景部分 */
  .career-section {
    background-color: #fff;
  }

  .career-section .text-content {
    flex: 1;
  }

  .highlight-text {
    font-size: 18px;
    color: #1a4f8c;
    margin-bottom: 30px;
    line-height: 1.8;
  }

  .career-paths {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 30px;
  }

  .career-path {
    background-color: #f0f7ff;
    padding: 20px;
    border-radius: 8px;
    transition: all 0.3s;
  }

  .career-path:hover {
    background-color: #e0f0ff;
  }

  .career-path h3 {
    font-size: 18px;
    color: #1a4f8c;
    margin-bottom: 5px;
  }

  .career-path p {
    font-size: 16px;
    color: #3c9af0;
    font-weight: 500;
  }

  .companies-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 10px;
  }

  .company-logos {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
  }

  .company {
    padding: 8px 15px;
    background-color: #f0f7ff;
    border-radius: 20px;
    font-size: 14px;
    color: #1a4f8c;
  }

  .career-image-container {
    position: relative;
    width: 100%;
    max-width: 500px;
  }

  .career-image-wrapper {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }

  .career-image {
    width: 100%;
    display: block;
  }

  /* 荣誉成果部分 */
  .awards-section {
    background-color: #f0f7ff;
  }

  .awards-section .text-content {
    flex: 1;
  }

  .awards-section p {
    font-size: 16px;
    line-height: 1.8;
    margin-bottom: 20px;
  }

  .awards-list {
    padding-left: 20px;
  }

  .awards-list li {
    margin-bottom: 15px;
    font-size: 16px;
    position: relative;
    padding-left: 15px;
  }

  .awards-list li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 10px;
    width: 6px;
    height: 6px;
    background-color: #3c9af0;
    border-radius: 50%;
  }

  .awards-image {
    width: 100%;
    max-width: 500px;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }

  /* 底部部分 */
  .footer {
    background-color: #1a4f8c;
    color: #fff;
    padding: 60px 0 20px;
  }

  .footer-content {
    display: flex;
    justify-content: space-between;
    margin-bottom: 40px;
  }

  .footer-logo {
    margin-right: 40px;
  }

  .footer-logo-img {
    height: 100px;
    margin-bottom: 15px;
  }

  .footer-logo p {
    font-size: 16px;
    opacity: 0.8;
  }

  .footer-links {
    display: flex;
    flex: 1;
    justify-content: space-between;
  }

  .footer-column h3 {
    font-size: 18px;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
  }

  .footer-column h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: #3c9af0;
  }

  .footer-column a,
  .footer-column p {
    display: block;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 10px;
    text-decoration: none;
    transition: all 0.3s;
  }

  .footer-column a:hover {
    color: #fff;
    transform: translateX(5px);
  }

  .copyright {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 14px;
    opacity: 0.7;
  }

  /* 响应式设计 */
  @media (max-width: 1024px) {
    .section-content {
      flex-direction: column;
    }

    .features-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .about-image,
    .career-image-container,
    .awards-image {
      margin-top: 30px;
    }
  }

  @media (max-width: 768px) {
    .header {
      padding: 15px 20px;
    }

    .nav-links {
      display: none;
    }

    .section {
      padding: 60px 0;
    }

    .section-title {
      font-size: 28px;
    }

    .features-grid {
      grid-template-columns: 1fr;
    }

    .career-paths {
      grid-template-columns: 1fr;
    }

    .footer-content {
      flex-direction: column;
    }

    .footer-logo {
      margin-bottom: 30px;
    }

    .footer-links {
      flex-wrap: wrap;
    }

    .footer-column {
      width: 50%;
      margin-bottom: 30px;
    }
  }

  /* 自定义走马灯样式 */
  :deep(.el-carousel__item) {
    border-radius: 8px;
  }

  :deep(.el-carousel__item:not(.is-active)) {
    filter: brightness(0.7) blur(2px);
    transform: scale(0.85);
  }

  :deep(.el-carousel__arrow) {
    background-color: rgba(26, 79, 140, 0.7);
    border-radius: 50%;
    width: 44px;
    height: 44px;
  }

  :deep(.el-carousel__indicators) {
    transform: translateY(15px);
  }

  :deep(.el-carousel__indicator) {
    padding: 0 8px;
  }

  :deep(.el-carousel__indicator--active button) {
    background-color: #1a4f8c;
  }

  :deep(.el-carousel__button) {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(26, 79, 140, 0.3);
  }

  .activity-modal-fab {
    position: fixed;
    top: 120px;
    right: 40px;
    z-index: 2000;
    box-shadow: 0 2px 10px rgba(60, 154, 240, 0.15);
    border-radius: 24px;
    padding: 16px 24px;
    font-size: 16px;
  }

  .side-toolbar {
    position: fixed;
    top: 60%;
    right: 30px;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 18px;
    transform: translateY(-50%);
  }

  .toolbar-item {
    width: 60px;
    height: 60px;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 2px 10px rgba(60, 154, 240, 0.12);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 13px;
    color: #1a4f8c;
    border: 1px solid #e6eaf0;
  }

  .toolbar-item:hover {
    background: linear-gradient(90deg, #1a4f8c, #3c9af0);
    color: #fff;
  }

  .toolbar-item .el-icon {
    font-size: 24px;
    margin-bottom: 4px;
  }

  .activity-signup-btn {
    /* 取消默认高亮，仅在悬停时高亮 */
    background: #fff;
    color: #1a4f8c;
    font-weight: bold;
  }

  .activity-signup-btn:hover {
    background: linear-gradient(90deg, #1989fa, #67c23a);
    color: #fff;
  }

  .side-toolbar-wrapper {
    position: fixed;
    top: 60%;
    right: 30px;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    transform: translateY(-50%);
  }

  .side-triangle {
    width: 0;
    height: 0;
    border-top: 18px solid transparent;
    border-bottom: 18px solid transparent;
    border-left: 22px solid #1989fa;
    margin-right: 2px;
    cursor: pointer;
    transition: border-left-color 0.2s;
  }

  .side-triangle.open {
    border-left-color: #67c23a;
  }

  .fade-slide-enter-active,
  .fade-slide-leave-active {
    transition: all 0.25s cubic-bezier(0.55, 0, 0.1, 1);
  }

  .fade-slide-enter-from,
  .fade-slide-leave-to {
    opacity: 0;
    transform: translateX(40px);
  }

  .fade-slide-enter-to,
  .fade-slide-leave-from {
    opacity: 1;
    transform: translateX(0);
  }

  .rankings-section {
    max-width: 1400px;
    margin: 40px auto 0 auto;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: flex-start;
    gap: 60px;
    padding: 0 20px;
  }

  .ranking-card {
    margin-bottom: 80px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(60, 154, 240, 0.08);
    min-height: 600px;
    /* 新增宽度设置 */
    width: 360px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
  }

  .ranking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 18px;
  }

  .ranking-title {
    font-size: 20px;
    color: #1a4f8c;
    margin: 0;
    font-weight: bold;
  }

  .toggle-ranking-btn {
    background: #ffffff;
    border: 1px solid #1a4f8c;
    color: #1a4f8c;
    border-radius: 16px;
    padding: 6px 16px;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 6px rgba(60, 154, 240, 0.1);
  }

  .toggle-ranking-btn:hover {
    background: #f0f7ff;
    border-color: #3c9af0;
    color: #3c9af0;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(60, 154, 240, 0.2);
  }

  .ranking-list {
    list-style: none;
    padding: 0 12px;
    margin: 0;
  }

  .ranking-list li {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
    font-size: 16px;
  }

  .ranking-list li:last-child {
    border-bottom: none;
  }

  .rank-index {
    width: 28px;
    text-align: center;
    font-weight: bold;
    color: #409eff;
  }

  .rank-name {
    flex: 1;
    margin-left: 10px;
    color: #333;
  }

  .rank-score {
    color: #f56c6c;
    font-weight: bold;
    min-width: 60px;
    text-align: right;
  }

  .rank-score-green {
    color: #67c23a;
  }

  .rank-score-blue {
    color: #409eff;
  }

  .rank-score-red {
    color: #f56c6c;
  }

  .score-marquee {
    margin-top: 18px;
    background: #f8fafd;
    border-radius: 8px;
    padding: 0 12px;
    min-height: 36px;
    align-items: center;
    box-shadow: 0 1px 4px rgba(60, 154, 240, 0.06);
  }

  .score-marquee-green {
    border-left: 4px solid #67c23a;
  }

  .score-marquee-red {
    border-left: 4px solid #f56c6c;
  }

  .marquee-icon {
    margin-right: 8px;
    font-size: 18px;
    transition: background-color 0.2s;
  }

  .marquee-content {
    font-size: 15px;
    color: #67c23a;
    font-weight: bold;
  }

  /* 加分样式 */
  .score-marquee.add {
    background: #d4f9e2; /* 浅绿色背景 */
  }

  .marquee-icon.add-icon {
    color: #28a745; /* 加分图标颜色 */
  }

  .marquee-content.add-content {
    color: #28a745; /* 加分文字颜色 */
  }

  /* 减分样式 */
  .score-marquee.subtract {
    background: #ffe6e6; /* 浅红色背景 */
  }

  .marquee-icon.subtract-icon {
    color: #dc3545; /* 减分图标颜色 */
  }

  .marquee-content.subtract-content {
    color: #dc3545; /* 减分文字颜色 */
  }

  /* 后十名排名样式 */
  .rank-score-red {
    color: #f56c6c;
    font-weight: bold;
  }


  /* 减分轮播 - 强制向下滚动 */
  .score-marquee-red .el-carousel__container {
    display: flex;
    flex-direction: column-reverse;
    animation-play-state: running !important;
  }

  /* 覆盖悬停暂停的默认行为 */
  .score-marquee-red .el-carousel:hover .el-carousel__container {
    animation-play-state: running !important;
  }

  /* 初始位置和动画 */
  .score-marquee-red .el-carousel__item {
    transform: translateY(100%) !important;
  }

  .score-marquee-red .el-carousel__item.is-active {
    transform: translateY(0) !important;
  }

  .score-marquee-red .el-carousel__item.is-animating {
    transition: transform 0.5s ease-in-out !important;
  }

  .report-card {
    min-height: 600px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(60, 154, 240, 0.08);
    width: 700px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
  }

  .report-carousel {
    margin-top: 18px;
    background: #f8fafd;
    border-radius: 8px;
    padding: 0 12px;
    min-height: 280px;
    box-shadow: 0 1px 4px rgba(60, 154, 240, 0.06);
    display: flex;
    align-items: center;
  }

  .report-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 18px 20px;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 1px 4px rgba(60, 154, 240, 0.04);
    min-height: 140px;
  }

  .report-item.violation {
    border-left: 6px solid #f56c6c;
  }

  .report-item.dorm_violation {
    border-left: 6px solid #e6a23c;
  }

  .report-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .report-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 10px;
  }

  .report-type.violation {
    color: #f56c6c;
    font-weight: bold;
    font-size: 16px;
  }

  .report-type.dorm_violation {
    color: #e6a23c;
    font-weight: bold;
    font-size: 16px;
  }

  .report-reason {
    font-size: 16px;
    color: #333;
    margin-bottom: 12px;
  }

  .report-points {
    font-size: 18px;
    font-weight: bold;
    color: #409eff;
  }

  .report-info {
    min-width: 220px;
    text-align: right;
    font-size: 16px;
    color: #666;
    font-weight: 500;
  }

  .report-images {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    flex-wrap: wrap;
    max-width: 300px;
  }

  .evidence-image {
    width: 50px;
    height: 50px;
    border-radius: 4px;
    border: 1px solid #e6e6e6;
    cursor: pointer;
    transition: transform 0.2s;
    object-fit: cover;
  }

  .evidence-image:hover {
    transform: scale(1.05);
    border-color: #409eff;
  }

  .image-error {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background-color: #f5f7fa;
    border-radius: 4px;
    color: #c0c4cc;
    font-size: 24px;
  }

  /* 违纪通报卡片样式 */
  .violation-card {
    min-height: 600px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(60, 154, 240, 0.08);
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
  }

  .violation-title {
    text-align: center;
    font-size: 20px;
    color: #1a4f8c;
    margin-bottom: 18px;
    font-weight: bold;
  }

  .violation-carousel {
    margin-top: 18px;
    background: #f8fafd;
    border-radius: 8px;
    padding: 0 12px;
    min-height: 300px;
    box-shadow: 0 1px 4px rgba(60, 154, 240, 0.06);
    display: flex;
    align-items: center;
  }

  .violation-item {
    display: flex;
    flex-direction: column;
    padding: 20px;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 1px 4px rgba(60, 154, 240, 0.04);
    min-height: 260px;
    border-left: 6px solid #f56c6c;
  }

  .violation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
  }

  .student-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }

  .student-name {
    font-size: 18px;
    font-weight: bold;
    color: #1a4f8c;
  }

  .class-name {
    font-size: 14px;
    color: #666;
  }

  .violation-tag {
    background: #f56c6c;
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
  }

  .violation-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .violation-reason {
    display: flex;
    align-items: flex-start;
    gap: 8px;
  }

  .reason-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
    white-space: nowrap;
  }

  .reason-text {
    font-size: 14px;
    color: #333;
    line-height: 1.5;
    flex: 1;
  }

  .violation-points {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .points-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
  }

  .points-value {
    font-size: 16px;
    color: #f56c6c;
    font-weight: bold;
  }

  .evidence-images {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .evidence-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
  }

  .image-grid {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .evidence-image {
    width: 60px;
    height: 60px;
    border-radius: 4px;
    border: 1px solid #e6e6e6;
    cursor: pointer;
    transition: transform 0.2s;
    object-fit: cover;
  }

  .evidence-image:hover {
    transform: scale(1.05);
    border-color: #409eff;
  }

  .violation-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #909399;
  }

  .empty-icon {
    font-size: 60px;
    margin-bottom: 15px;
    color: #c0c4cc;
  }

  .empty-text {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 5px;
    color: #909399;
  }

  .empty-subtext {
    font-size: 16px;
    color: #c0c4cc;
  }

  .student1 {
    padding-top: 100px;
    padding-left: 100px;
    font-weight: bold;
  }

  .student2 {
    color: red;
  }

  /* 违纪通报新样式 */
  .violation-card-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 25px;
    gap: 25px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
  }

  .violation-card-content:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  .violation-image-container {
    flex: 0 0 55%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .violation-image {
    max-width: 100%;
    max-height: 400px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    object-fit: cover;
  }

  .violation-text-container {
    flex: 0 0 40%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .violation-info {
    display: flex;
    flex-direction: column;
    gap: 18px;
  }

  .violation-person {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .person-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
  }

  .person-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .class-name {
    font-size: 16px;
    color: #333;
    font-weight: 500;
  }

  .student-name {
    font-size: 18px;
    color: #1a4f8c;
    font-weight: bold;
  }

  .violation-points {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .points-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
  }

  .points-value {
    font-size: 20px;
    color: #f56c6c;
    font-weight: bold;
  }



  /* 卡片轮播样式优化 */
  :deep(.el-carousel--vertical.el-carousel--card) {
    border-radius: 8px;
    overflow: hidden;
  }

  :deep(.el-carousel--vertical.el-carousel--card .el-carousel__item) {
    border-radius: 8px;
    background: transparent;
  }

  :deep(.el-carousel--vertical.el-carousel--card .el-carousel__item:not(.is-active)) {
    filter: brightness(0.8) blur(0.5px);
    transform: scale(0.95);
  }

  :deep(.el-carousel--vertical.el-carousel--card .el-carousel__item.is-active) {
    transform: scale(1);
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .rankings-section {
      max-width: 100%;
      gap: 40px;
      padding: 0 15px;
    }
  }

  @media (max-width: 768px) {
    .rankings-section {
      flex-direction: column;
      gap: 30px;
      align-items: center;
    }

    .violation-card-content {
      flex-direction: column;
      gap: 20px;
      padding: 20px;
    }

    .violation-image-container {
      flex: 0 0 auto;
    }

    .violation-text-container {
      flex: 0 0 auto;
    }

    .violation-image {
      max-height: 250px;
    }
  }



  .evidence-image img {
    max-width: 100%;
    max-height: 300px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    object-fit: contain;
  }

  /* 悬停提示框 */
  .violation-tooltip {
    position: fixed;
    background: linear-gradient(135deg, #fff 0%, #f8fafd 100%);
    border: 1px solid #e6e6e6;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    max-width: 320px;
    min-width: 280px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .tooltip-content {
    padding: 16px;
    position: relative;
  }

  .tooltip-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e6e6e6;
  }

  .tooltip-title {
    font-size: 16px;
    font-weight: bold;
    color: #1a4f8c;
    display: flex;
    align-items: center;
  }

  .tooltip-title::before {
    content: "⚠️";
    margin-right: 8px;
    font-size: 18px;
  }

  .tooltip-body {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .tooltip-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .tooltip-person {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: rgba(64, 158, 255, 0.1);
    border-radius: 6px;
    border-left: 3px solid #409eff;
  }

  .tooltip-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
    min-width: 60px;
  }

  .tooltip-value {
    font-size: 14px;
    color: #333;
    font-weight: 500;
    margin-left: 8px;
  }

  .tooltip-reason {
    padding: 8px 12px;
    background: rgba(245, 108, 108, 0.1);
    border-radius: 6px;
    border-left: 3px solid #f56c6c;
  }

  .tooltip-reason-text {
    font-size: 13px;
    color: #333;
    line-height: 1.5;
    margin-top: 6px;
    word-break: break-word;
    white-space: pre-wrap;
  }

  .tooltip-arrow {
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid #e6e6e6;
  }

  .tooltip-arrow::after {
    content: '';
    position: absolute;
    top: -9px;
    left: -8px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid #fff;
  }

  .tooltip-fade-enter-active,
  .tooltip-fade-leave-active {
    transition: opacity 0.3s;
  }

  .tooltip-fade-enter-from,
  .tooltip-fade-leave-to {
    opacity: 0;
  }

  .tooltip-fade-enter-to,
  .tooltip-fade-leave-from {
    opacity: 1;
  }

</style>
