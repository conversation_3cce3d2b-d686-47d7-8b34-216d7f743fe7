package com.zhentao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhentao.dto.BatchImportDto;
import com.zhentao.dto.BatchImportResultDto;
import com.zhentao.mapper.EduStudentMapper;
import com.zhentao.mapper.SysUserMapper;
import com.zhentao.mapper.SysUserRoleMapper;
import com.zhentao.pojo.*;
import com.zhentao.service.*;
import com.zhentao.utils.Result;
import com.zhentao.utils.UserContext;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.swing.plaf.synth.SynthDesktopIconUI;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 学生表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Service
public class EduStudentServiceImpl extends ServiceImpl<EduStudentMapper, EduStudent> implements EduStudentService {
    @Autowired
    private EduStudentMapper eduStudentMapper;

    @Autowired
    private EduClassService eduClassService;

    @Autowired
    private EduStageService eduStageService;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private OldStuService oldStuService;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Override
    public Result queryTopTenStudents() {
        //查询积分前十名的同学
        QueryWrapper<EduStudent> wrapper = new QueryWrapper<>();
        wrapper.orderByDesc("points").last("LIMIT 10");
        List<EduStudent> eduStudents = eduStudentMapper.selectList(wrapper);

        if (eduStudents == null) {
            return Result.ERROR("没有查询到数据");
        }

        for (EduStudent eduStudent : eduStudents) {
            eduStudent.setClassName(eduClassService.getById(eduStudent.getClassId()).getClassName());
        }
        return Result.OK(eduStudents);
    }

    @Override
    public Result queryBottomTenStudents() {
        //查询积分最低的10个同学
        QueryWrapper<EduStudent> wrapper = new QueryWrapper<>();
        wrapper.lt("points", 100);
        wrapper.orderByAsc("points").last("LIMIT 10");
        List<EduStudent> eduStudents = eduStudentMapper.selectList(wrapper);

        if (eduStudents == null) {
            return Result.ERROR("没有查询到数据");
        }

        for (EduStudent eduStudent : eduStudents) {
            eduStudent.setClassName(eduClassService.getById(eduStudent.getClassId()).getClassName());
        }
        return Result.OK(eduStudents);
    }


    @Override
    public Result queryTopTenStudentsByClass(Integer classId) {
        //查询本班级积分前十名的同学
        QueryWrapper<EduStudent> wrapper = new QueryWrapper<>();
        wrapper.eq("class_id",classId);
        wrapper.orderByDesc("points").last("LIMIT 10");
        List<EduStudent> eduStudents = eduStudentMapper.selectList(wrapper);

        if (eduStudents == null) {
            return Result.ERROR("没有查询到数据");
        }

        for (EduStudent eduStudent : eduStudents) {
            eduStudent.setClassName(eduClassService.getById(eduStudent.getClassId()).getClassName());
        }
        return Result.OK(eduStudents);
    }

    @Override
    public Result queryBottomTenStudentsByClass(Integer classId) {
        //查询本班级积分最低的10个同学
        QueryWrapper<EduStudent> wrapper = new QueryWrapper<>();
        wrapper.eq("class_id",classId);
        wrapper.lt("points", 100);
        wrapper.orderByAsc("points").last("LIMIT 10");
        List<EduStudent> eduStudents = eduStudentMapper.selectList(wrapper);

        if (eduStudents == null) {
            return Result.ERROR("没有查询到数据");
        }

        for (EduStudent eduStudent : eduStudents) {
            eduStudent.setClassName(eduClassService.getById(eduStudent.getClassId()).getClassName());
        }
        return Result.OK(eduStudents);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchImportStudents(MultipartFile file) throws Exception {
        // 结果统计
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> errorList = new ArrayList<>();
        int totalCount = 0;
        int successCount = 0;
        int failCount = 0;
        long startTime = System.currentTimeMillis();

        // 获取所有班级信息，用于验证
        List<EduClass> allClasses = eduClassService.list();
        Map<String, Integer> classNameToIdMap = new HashMap<>();
        for (EduClass eduClass : allClasses) {
            classNameToIdMap.put(eduClass.getClassName(), eduClass.getClassId());
        }

        try (InputStream inputStream = file.getInputStream()) {
            Workbook workbook;

            // 根据文件后缀判断Excel版本
            String fileName = file.getOriginalFilename();
            if (fileName == null) {
                throw new Exception("文件名为空");
            }

            fileName = fileName.toLowerCase();

            if (fileName.endsWith(".xlsx") || fileName.endsWith(".et") || fileName.endsWith(".ett")) {
                workbook = new XSSFWorkbook(inputStream);
            } else if (fileName.endsWith(".xls")) {
                workbook = new HSSFWorkbook(inputStream);
            } else {
                throw new Exception("不支持的文件格式，请上传.xlsx或.xls格式的Excel文件");
            }

            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);

            // 从第二行开始读取数据（第一行是表头）
            int firstRowNum = sheet.getFirstRowNum() + 1;
            int lastRowNum = sheet.getLastRowNum();

            // 遍历每一行
            for (int rowNum = firstRowNum; rowNum <= lastRowNum; rowNum++) {
                Row row = sheet.getRow(rowNum);
                if (row == null) {
                    continue;
                }

                totalCount++;

                try {
                    // 解析学生信息
                    EduStudent student = new EduStudent();

                    // 学号
                    String studentNo = getCellValueAsString(row.getCell(0));
                    if (studentNo == null || studentNo.trim().isEmpty()) {
                        throw new Exception("学号不能为空");
                    }
                    student.setStudentNo(studentNo);

                    // 检查学号是否已存在
                    /*LambdaQueryWrapper<EduStudent> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(EduStudent::getStudentNo, studentNo);
                    if (count(queryWrapper) > 0) {
                        throw new Exception("学号已存在");
                    }*/

                    // 姓名
                    String realName = getCellValueAsString(row.getCell(1));
                    if (realName == null || realName.trim().isEmpty()) {
                        throw new Exception("姓名不能为空");
                    }
                    student.setRealName(realName);

                    // 性别
                    String gender = getCellValueAsString(row.getCell(2));
                    if ("男".equals(gender) || "1".equals(gender)) {
                        student.setGender(1);
                    } else if ("女".equals(gender) || "0".equals(gender)) {
                        student.setGender(0);
                    } else {
                        throw new Exception("性别必须为'男'或'女'");
                    }

                    // 班级
                    String className = getCellValueAsString(row.getCell(3));
                    if (className == null || className.trim().isEmpty()) {
                        throw new Exception("班级不能为空");
                    }

                    // 查找班级ID
                    Integer classId = findClassIdByName(className, classNameToIdMap);

                    // 如果找不到匹配的班级
                    if (classId == null) {
                        // 记录当前班级名和所有可用班级，方便调试
                        StringBuilder availableClasses = new StringBuilder();
                        for (String availableClass : classNameToIdMap.keySet()) {
                            availableClasses.append(availableClass).append(", ");
                        }
                        String availableClassesStr = availableClasses.length() > 0 ?
                                availableClasses.substring(0, availableClasses.length() - 2) : "无";
                        throw new Exception("班级 '" + className + "' 不存在。可用班级: " + availableClassesStr);
                    }

                    student.setClassId(classId);

                    // 手机号
                    String phone = getCellValueAsString(row.getCell(4));
                    student.setPhone(phone);

                    // 邮箱
                    String email = getCellValueAsString(row.getCell(5));
                    student.setEmail(email);

                    // 备注
                    String remark = getCellValueAsString(row.getCell(6));
                    student.setRemark(remark);

                    // 设置默认值
                    student.setPoints(100); // 默认积分
                    student.setStatus(0); // 默认状态：正常
                    student.setDelFlag(0); // 默认未删除
                    student.setCreateTime(new Date());

                    // 获取当前用户ID作为创建者
                    try {
                        Integer currentUserId = UserContext.getCurrentUserId();
                        if (currentUserId != null) {
                            student.setCreateBy(currentUserId.longValue());
                        }
                    } catch (Exception e) {
                        // 如果获取不到，则不设置创建者
                        System.out.println("无法获取当前用户信息: " + e.getMessage());
                    }

                    // 保存学生信息
                    EduStudent studentNo1 = eduStudentMapper.selectOne(new QueryWrapper<EduStudent>().eq("student_no", student.getStudentNo()));
                    boolean saveResult = false;
                    if (studentNo1 == null) {
                        saveResult = save(student);
                    } else {
                        student.setStudentId(studentNo1.getStudentId());
                        eduStudentMapper.updateById(student);
                        saveResult = true;
                    }

                    if (saveResult) {
                        successCount++;
                    } else {
                        throw new Exception("保存失败");
                    }

                } catch (Exception e) {
                    failCount++;

                    // 记录错误信息
                    Map<String, Object> error = new HashMap<>();
                    error.put("row", rowNum + 1);
                    error.put("id", getCellValueAsString(row.getCell(0)));
                    error.put("name", getCellValueAsString(row.getCell(1)));
                    error.put("errorMsg", e.getMessage());
                    errorList.add(error);
                }
            }

            workbook.close();

        } catch (Exception e) {
            throw new Exception("解析Excel文件失败：" + e.getMessage());
        }

        // 计算耗时
        long endTime = System.currentTimeMillis();
        double time = (endTime - startTime) / 1000.0;

        // 组装结果
        result.put("totalCount", totalCount);
        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("time", time);
        result.put("errors", errorList);

        return result;
    }

    /**
     * 获取单元格的值并转换为字符串
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 对于数值型数据，如果是整数则不需要小数点
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((int) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                boolean boolValue = cell.getBooleanCellValue();
                // 布尔值转换为1/0，可用于性别等字段
                return boolValue ? "1" : "0";
            case FORMULA:
                try {
                    return String.valueOf(cell.getNumericCellValue());
                } catch (Exception e) {
                    try {
                        return cell.getStringCellValue();
                    } catch (Exception ex) {
                        return cell.getCellFormula();
                    }
                }
            default:
                return null;
        }
    }

    /**
     * 查找班级ID，支持不区分大小写和空格的匹配
     *
     * @param className        班级名称
     * @param classNameToIdMap 班级名称到ID的映射
     * @return 班级ID，如果不存在返回null
     */
    private Integer findClassIdByName(String className, Map<String, Integer> classNameToIdMap) {
        if (className == null || className.trim().isEmpty()) {
            return null;
        }

        // 规范化输入的班级名称：去除空格并转换为小写
        String normalizedInputClassName = className.toLowerCase().trim();

        // 1. 首先尝试直接精确匹配
        Integer classId = classNameToIdMap.get(className);
        if (classId != null) {
            return classId;
        }

        // 2. 尝试不区分大小写和空格的匹配
        for (Map.Entry<String, Integer> entry : classNameToIdMap.entrySet()) {
            String mapClassName = entry.getKey();
            String normalizedMapClassName = mapClassName.toLowerCase().trim();

            // 完全匹配（忽略大小写和空格）
            if (normalizedMapClassName.equals(normalizedInputClassName)) {
                return entry.getValue();
            }

            // 3. 允许数字与中文数字的差异（如"2408A"和"二四零八A"）
            // 这部分代码可以根据实际需求扩展
        }

        // 4. 如果还是没找到，尝试部分匹配（如输入"2408"能匹配"2408A"）
        for (Map.Entry<String, Integer> entry : classNameToIdMap.entrySet()) {
            String mapClassName = entry.getKey();
            String normalizedMapClassName = mapClassName.toLowerCase().trim();

            if (normalizedMapClassName.contains(normalizedInputClassName) ||
                    normalizedInputClassName.contains(normalizedMapClassName)) {
                return entry.getValue();
            }
        }

        // 没有找到匹配的班级
        return null;
    }

    @Override
    public List<EduStudent> searchStudents(String keyword) {
        // 获取当前用户信息，实现权限控制
        SysUser currentUser = UserContext.getCurrentUser();

        if (currentUser != null) {
            Integer userId = currentUser.getUserId();

            // 查询用户的角色
            QueryWrapper<SysUserRole> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            List<SysUserRole> userRoles = sysUserRoleMapper.selectList(queryWrapper);

            List<Integer> roleIds = userRoles.stream()
                    .map(SysUserRole::getRoleId)
                    .collect(Collectors.toList());

            System.out.println("学生搜索 - 当前用户角色ID列表: " + roleIds);

            // 角色权限控制：按最高权限角色判断
            // 权限从高到低：1 > 2,3 > 4,5 > 6 > 8
            if (roleIds.contains(1)) {
                // 超级管理员(1)可以搜索所有学生
                System.out.println("用户具有超级管理员角色，可以搜索所有学生");
            } else if (roleIds.contains(2) || roleIds.contains(3)) {
                // 专业主任(2)、专病主任(3)可以搜索所有学生
                System.out.println("用户具有主任角色，可以搜索所有学生");
            } else if (roleIds.contains(4) || roleIds.contains(5)) {
                // 讲师(4)、导员(5)：只能搜索自己班级的学生
                System.out.println("用户具有讲师/导员角色，限制只能搜索自己班级的学生，用户ID: " + userId);
                return searchStudentsForTeacher(keyword, userId);
            } else if (roleIds.contains(6)) {
                // 秘书(6)可以搜索所有学生
                System.out.println("用户具有秘书角色，可以搜索所有学生");
            } else if (roleIds.contains(8)) {
                // 文书(8)：只能搜索自己班级的学生
                System.out.println("用户具有文书角色，限制只能搜索自己班级的学生，用户ID: " + userId);
                return searchStudentsForSecretary(keyword, userId);
            }
        }

        // Handle empty keyword case
        if (keyword == null || keyword.trim().isEmpty()) {
            // Return limited results when no keyword is provided
            return eduStudentMapper.searchStudentsLimited();
        }
        // Using SQL to join with class table to get class name
        return eduStudentMapper.searchStudents(keyword);
    }

    /**
     * 讲师/导员搜索自己班级的学生
     */
    private List<EduStudent> searchStudentsForTeacher(String keyword, Integer teacherId) {
        // Handle empty keyword case
        if (keyword == null || keyword.trim().isEmpty()) {
            keyword = "";
        }
        // 使用教师ID查询自己班级的学生
        return eduStudentMapper.searchStudentsForTeacher(keyword, teacherId);
    }

    /**
     * 文书搜索自己班级的学生
     */
    private List<EduStudent> searchStudentsForSecretary(String keyword, Integer secretaryUserId) {
        try {
            // Handle empty keyword case
            if (keyword == null || keyword.trim().isEmpty()) {
                keyword = "";
            }

            // 获取文书所在的班级ID
            Integer classId = getSecretaryClassId(secretaryUserId);
            if (classId == null) {
                System.out.println("文书用户ID " + secretaryUserId + " 未找到对应的班级信息");
                return new ArrayList<>();
            }

            System.out.println("文书用户ID " + secretaryUserId + " 所在班级ID: " + classId + ", 搜索关键词: '" + keyword + "'");

            // 先查询该班级的所有学生（用于调试）
            LambdaQueryWrapper<EduStudent> debugWrapper = new LambdaQueryWrapper<>();
            debugWrapper.eq(EduStudent::getClassId, classId);
            debugWrapper.eq(EduStudent::getDelFlag, "0");
            debugWrapper.eq(EduStudent::getStatus, 0);
            List<EduStudent> allClassStudents = eduStudentMapper.selectList(debugWrapper);
            System.out.println("班级ID " + classId + " 中共有 " + allClassStudents.size() + " 个学生:");
            for (EduStudent student : allClassStudents) {
                System.out.println("  - 学号: " + student.getStudentNo() + ", 姓名: " + student.getRealName());
            }

            // 使用班级ID查询班级内的学生
            List<EduStudent> result = eduStudentMapper.searchStudentsByClass(keyword, classId);
            System.out.println("搜索结果: " + result.size() + " 个学生");
            for (EduStudent student : result) {
                System.out.println("  - 搜索到: 学号: " + student.getStudentNo() + ", 姓名: " + student.getRealName());
            }

            return result;
        } catch (Exception e) {
            System.err.println("文书搜索学生失败: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * 获取文书所在的班级ID
     * 通过学生表查找文书对应的学生记录，获取班级ID
     */
    private Integer getSecretaryClassId(Integer secretaryUserId) {
        try {
            // 获取文书用户信息
            SysUser secretary = sysUserMapper.selectById(secretaryUserId);
            if (secretary == null) {
                return null;
            }

            // 通过用户名（假设用户名就是学号）查找学生记录
            LambdaQueryWrapper<EduStudent> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(EduStudent::getStudentNo, secretary.getUsername());
            wrapper.eq(EduStudent::getDelFlag, "0");
            EduStudent student = eduStudentMapper.selectOne(wrapper);

            // 如果没有找到对应的学生记录，尝试创建一个
            if (student == null) {
                System.out.println("文书用户 " + secretary.getUsername() + " 没有对应的学生记录，尝试创建...");
                student = createSecretaryStudentRecord(secretary);
            }

            return student != null ? student.getClassId() : null;
        } catch (Exception e) {
            System.err.println("获取文书班级ID失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 为文书用户创建对应的学生记录
     */
    private EduStudent createSecretaryStudentRecord(SysUser secretary) {
        try {
            // 获取一个现有的班级ID
            Integer availableClassId = getAvailableClassId();
            if (availableClassId == null) {
                System.err.println("没有找到可用的班级，无法创建学生记录");
                return null;
            }

            EduStudent student = new EduStudent();
            student.setStudentNo(secretary.getUsername());
            student.setRealName(secretary.getRealName());
            student.setGender(secretary.getGender());
            student.setPhone(secretary.getPhone());
            student.setEmail(secretary.getEmail());
            student.setClassId(availableClassId); // 使用现有的班级ID
            student.setPoints(100); // 初始积分100
            student.setStatus(0); // 正常状态
            student.setCreateBy(1L); // 系统创建
            student.setCreateTime(new Date());
            student.setDelFlag(0); // 未删除

            boolean success = save(student);
            if (success) {
                System.out.println("成功为文书用户 " + secretary.getUsername() + " 创建学生记录，班级ID: " + student.getClassId());

                // 创建一些测试学生数据（仅在第一次创建文书记录时）
                createTestStudentsForClass(availableClassId);

                return student;
            } else {
                System.err.println("为文书用户 " + secretary.getUsername() + " 创建学生记录失败");
                return null;
            }
        } catch (Exception e) {
            System.err.println("创建文书学生记录失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 获取一个可用的班级ID
     */
    private Integer getAvailableClassId() {
        try {
            // 查找第一个可用的班级
            LambdaQueryWrapper<EduClass> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(EduClass::getDelFlag, 0);
            wrapper.eq(EduClass::getStatus, 0);
            wrapper.last("LIMIT 1");

            EduClass availableClass = eduClassService.getOne(wrapper);
            if (availableClass != null) {
                System.out.println("找到可用班级: " + availableClass.getClassId() + " - " + availableClass.getClassName());
                return availableClass.getClassId();
            }

            System.err.println("没有找到可用的班级");
            return null;
        } catch (Exception e) {
            System.err.println("获取可用班级ID失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 为指定班级创建测试学生数据
     */
    private void createTestStudentsForClass(Integer classId) {
        try {
            // 先清理可能的重复数据
            cleanupDuplicateStudents();

            // 检查班级是否已有其他学生（除了文书）
            LambdaQueryWrapper<EduStudent> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(EduStudent::getClassId, classId);
            wrapper.eq(EduStudent::getDelFlag, "0");
            wrapper.ne(EduStudent::getStudentNo, "sunshanshan"); // 排除文书自己
            long existingCount = count(wrapper);

            if (existingCount > 0) {
                System.out.println("班级 " + classId + " 已有 " + existingCount + " 个学生，跳过创建测试数据");
                return;
            }

            System.out.println("为班级 " + classId + " 创建测试学生数据...");

            // 获取班级信息来生成合适的学号前缀
            String studentNoPrefix = "TEST" + String.format("%02d", classId);

            // 创建测试学生数据
            String[] testStudents = {
                studentNoPrefix + "01,张三,1,13800000001,<EMAIL>,95",
                studentNoPrefix + "02,李四,0,13800000002,<EMAIL>,105",
                studentNoPrefix + "03,王五,1,13800000003,<EMAIL>,88",
                studentNoPrefix + "04,赵六,0,13800000004,<EMAIL>,92",
                studentNoPrefix + "05,钱七,1,13800000005,<EMAIL>,98"
            };

            for (String studentData : testStudents) {
                String[] parts = studentData.split(",");

                // 检查学生是否已存在
                LambdaQueryWrapper<EduStudent> checkWrapper = new LambdaQueryWrapper<>();
                checkWrapper.eq(EduStudent::getStudentNo, parts[0]);
                checkWrapper.eq(EduStudent::getDelFlag, "0");
                EduStudent existingStudent = getOne(checkWrapper);

                if (existingStudent != null) {
                    System.out.println("学生 " + parts[0] + " 已存在，跳过创建");
                    continue;
                }

                EduStudent testStudent = new EduStudent();
                testStudent.setStudentNo(parts[0]);
                testStudent.setRealName(parts[1]);
                testStudent.setGender(Integer.parseInt(parts[2]));
                testStudent.setPhone(parts[3]);
                testStudent.setEmail(parts[4]);
                testStudent.setClassId(classId);
                testStudent.setPoints(Integer.parseInt(parts[5]));
                testStudent.setStatus(0);
                testStudent.setCreateBy(1L);
                testStudent.setCreateTime(new Date());
                testStudent.setDelFlag(0);

                save(testStudent);
                System.out.println("创建测试学生: " + testStudent.getStudentNo() + " - " + testStudent.getRealName());
            }

        } catch (Exception e) {
            System.err.println("创建测试学生数据失败: " + e.getMessage());
        }
    }

    /**
     * 清理重复的学生数据
     */
    private void cleanupDuplicateStudents() {
        try {
            System.out.println("开始清理重复的学生数据...");

            // 查找具有相同手机号和姓名的重复记录
            String sql = "SELECT phone, real_name, COUNT(*) as cnt FROM edu_student " +
                        "WHERE del_flag = '0' AND phone IS NOT NULL AND real_name IS NOT NULL " +
                        "GROUP BY phone, real_name HAVING COUNT(*) > 1";

            // 这里我们简单处理：删除文书用户 sunshanshan 的重复记录，保留最新的一条
            LambdaQueryWrapper<EduStudent> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(EduStudent::getStudentNo, "sunshanshan");
            wrapper.eq(EduStudent::getDelFlag, "0");
            wrapper.orderByDesc(EduStudent::getCreateTime);

            List<EduStudent> duplicates = list(wrapper);
            if (duplicates.size() > 1) {
                System.out.println("发现文书用户 sunshanshan 有 " + duplicates.size() + " 条重复记录，保留最新的一条");

                // 保留第一条（最新的），删除其他的
                for (int i = 1; i < duplicates.size(); i++) {
                    EduStudent duplicate = duplicates.get(i);
                    duplicate.setDelFlag(1); // 标记为删除
                    updateById(duplicate);
                    System.out.println("删除重复记录: ID=" + duplicate.getStudentId() + ", 创建时间=" + duplicate.getCreateTime());
                }
            }

        } catch (Exception e) {
            System.err.println("清理重复数据失败: " + e.getMessage());
        }
    }


    /*
     * 下载学生信息导入模板
     * @param response HTTP响应
     */
    @Override
    public void downloadStudentTemplate(HttpServletResponse response) throws Exception {
        XSSFWorkbook workbook = null;
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("学生信息导入模板", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Cache-Control", "no-cache");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");

            // 创建Excel工作簿
            workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("学生信息导入模板");

            // 创建样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);

            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);

            // 创建表头
            Row headerRow = sheet.createRow(0);
            String[] headers = {"班级", "辅导员", "讲师", "教室号", "所属月度", "课程名称", "学生人数"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
                sheet.setColumnWidth(i, 20 * 256);
            }

            // 添加说明行
            Row noteRow = sheet.createRow(2);
            Cell noteCell = noteRow.createCell(0);
            noteCell.setCellValue("说明：请按照此模板格式填写班级和学生信息，从第8行开始填写学生数据");
            sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 6));

            // 写入响应流
            workbook.write(response.getOutputStream());
            response.flushBuffer();
        } catch (Exception e) {
            throw new Exception("生成模板失败：" + e.getMessage());
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /*
     * 批量导入学生信息（从DTO）
     * @param importData 导入数据
     * @return 导入结果
     */
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private com.zhentao.config.DatabaseConnectionConfig databaseConnectionConfig;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchImportResultDto batchImportStudentsFromDto(BatchImportDto importData) throws Exception {
        long startTime = System.currentTimeMillis();
        BatchImportResultDto result = new BatchImportResultDto();
        List<BatchImportResultDto.ImportDetailDto> details = new ArrayList<>();

        int totalCount = 0;
        int successCount = 0;
        int failCount = 0;

        try {
            // 1. 重置表的ID自增（在导入前执行）
            resetTableAutoIncrementForImport();

            // 2. 获取当前用户ID
            Integer currentUserId = null;
            try {
                currentUserId = UserContext.getCurrentUserId();
            } catch (Exception e) {
                System.out.println("无法获取当前用户信息: " + e.getMessage());
            }

            // 3. 过滤掉包含"实训"的班级
            List<BatchImportDto.ClassImportDto> filteredClasses = new ArrayList<>();
            for (BatchImportDto.ClassImportDto classDto : importData.getClasses()) {
                if (classDto.getStageName() != null && classDto.getStageName().contains("实训")) {
                    System.out.println("跳过包含'实训'的班级: " + classDto.getClassName() + ", stageName: " + classDto.getStageName());
                    continue;
                }
                filteredClasses.add(classDto);
            }

            // 4. 预加载所有需要的基础数据，避免N+1查询
            Map<String, Object> preloadedData = preloadBatchImportData(importData, filteredClasses);

            // 5. 批量处理班级信息（更新或创建）
            Map<String, Integer> classNameToIdMap = batchProcessClassInfoWithUpdate(filteredClasses, preloadedData);

            // 6. 批量验证和准备学生数据
            List<EduStudent> studentsToInsert = new ArrayList<>();

            for (BatchImportDto.ClassImportDto classDto : filteredClasses) {
                Integer classId = classNameToIdMap.get(classDto.getClassName());

                for (BatchImportDto.StudentImportDto studentDto : classDto.getStudents()) {
                    totalCount++;
                    BatchImportResultDto.ImportDetailDto detail = new BatchImportResultDto.ImportDetailDto();
                    detail.setClassName(classDto.getClassName());
                    detail.setStudentName(studentDto.getRealName());
                    detail.setStudentNo(studentDto.getStudentNo());

                    try {
                        // 预处理学生数据
                        EduStudent student = prepareStudentInfoOptimized(studentDto, classId, currentUserId, preloadedData);
                        studentsToInsert.add(student);
                        detail.setStatus("success");
                        successCount++;
                    } catch (Exception e) {
                        detail.setStatus("fail");
                        detail.setErrorMessage(e.getMessage());
                        failCount++;
                    }
                    details.add(detail);
                }
            }

            // 7. 批量插入所有学生数据
            if (!studentsToInsert.isEmpty()) {
                try {
                    System.out.println("开始批量插入 " + studentsToInsert.size() + " 条学生记录");
                    boolean batchSaveResult = saveBatch(studentsToInsert, 1000); // 每批1000条

                    if (!batchSaveResult) {
                        throw new Exception("批量保存学生信息失败");
                    }

                    System.out.println("批量插入完成，成功插入 " + studentsToInsert.size() + " 条记录");
                } catch (Exception e) {
                    // 批量保存异常处理
                    int failedStudents = studentsToInsert.size();
                    successCount -= failedStudents;
                    failCount += failedStudents;

                    // 更新详情状态
                    for (BatchImportResultDto.ImportDetailDto detail : details) {
                        if ("success".equals(detail.getStatus())) {
                            detail.setStatus("fail");
                            detail.setErrorMessage("批量保存异常: " + e.getMessage());
                        }
                    }

                    throw new Exception("批量保存学生信息失败: " + e.getMessage());
                }
            }

            // 8. 设置结果
            result.setTotalCount(totalCount);
            result.setSuccessCount(successCount);
            result.setFailCount(failCount);
            result.setDetails(details);

            // 9. 确保批量操作完成后清理连接状态
            try {
                // 使用专门的配置类来重置连接状态
                databaseConnectionConfig.flushBatchOperations();
                databaseConnectionConfig.resetConnectionState();
                System.out.println("批量导入后连接状态已重置");
            } catch (Exception e) {
                System.out.println("重置连接状态时出现警告: " + e.getMessage());
            }

            long endTime = System.currentTimeMillis();
            System.out.println("批量导入Service层处理完成，耗时: " + (endTime - startTime) + "ms");

            return result;
        } catch (Exception e) {
            System.err.println("批量导入Service层处理失败: " + e.getMessage());
            throw new Exception("批量导入失败：" + e.getMessage());
        }
    }


    /*
     * 预加载批量导入所需的基础数据，避免N+1查询
     * @param importData 导入数据
     * @param filteredClasses 过滤后的班级列表
     * @return 预加载的数据Map
     */
    private Map<String, Object> preloadBatchImportData(BatchImportDto importData, List<BatchImportDto.ClassImportDto> filteredClasses) {
        Map<String, Object> preloadedData = new HashMap<>();

        try {
            // 1. 预加载所有班级信息
            List<EduClass> allClasses = eduClassService.list();
            Map<String, EduClass> classNameMap = allClasses.stream()
                    .collect(Collectors.toMap(EduClass::getClassName, c -> c, (existing, replacement) -> existing));
            preloadedData.put("classNameMap", classNameMap);

            // 2. 预加载所有阶段信息
            List<EduStage> allStages = eduStageService.list();
            Map<String, EduStage> stageNameMap = allStages.stream()
                    .collect(Collectors.toMap(EduStage::getStageName, s -> s, (existing, replacement) -> existing));
            preloadedData.put("stageNameMap", stageNameMap);

            // 3. 预加载所有用户信息（讲师和辅导员）
            List<SysUser> allUsers = sysUserService.list();
            Map<String, SysUser> userNameMap = allUsers.stream()
                    .filter(user -> user.getDelFlag() == 0 && user.getStatus() == 0) // 只加载正常状态的用户
                    .collect(Collectors.toMap(SysUser::getRealName, u -> u, (existing, replacement) -> existing));
            preloadedData.put("userNameMap", userNameMap);
            
            // 按用户类型分别存储，便于快速查找
            Map<String, SysUser> teacherMap = allUsers.stream()
                    .filter(user -> user.getDelFlag() == 0 && user.getStatus() == 0 && user.getUserType() == 4) // 4-讲师
                    .collect(Collectors.toMap(SysUser::getRealName, u -> u, (existing, replacement) -> existing));
            preloadedData.put("teacherMap", teacherMap);
            
            Map<String, SysUser> counselorMap = allUsers.stream()
                    .filter(user -> user.getDelFlag() == 0 && user.getStatus() == 0 && user.getUserType() == 5) // 5-导员
                    .collect(Collectors.toMap(SysUser::getRealName, u -> u, (existing, replacement) -> existing));
            preloadedData.put("counselorMap", counselorMap);

            // 4. 收集所有学号，批量查询历史数据
            Set<String> allStudentNos = filteredClasses.stream()
                    .flatMap(classDto -> classDto.getStudents().stream())
                    .map(BatchImportDto.StudentImportDto::getStudentNo)
                    .filter(Objects::nonNull)
                    .map(String::trim)
                    .collect(Collectors.toSet());

            if (!allStudentNos.isEmpty()) {
                LambdaQueryWrapper<OldStu> oldStuWrapper = new LambdaQueryWrapper<>();
                oldStuWrapper.in(OldStu::getOldstuNo, allStudentNos)
                        .eq(OldStu::getDelFlag, 0);
                List<OldStu> oldStuList = oldStuService.list(oldStuWrapper);

                Map<String, OldStu> oldStuMap = oldStuList.stream()
                        .collect(Collectors.toMap(OldStu::getOldstuNo, o -> o, (existing, replacement) -> existing));
                preloadedData.put("oldStuMap", oldStuMap);
            }

            // 5. 批量检查学号是否已存在
            if (!allStudentNos.isEmpty()) {
                LambdaQueryWrapper<EduStudent> existingWrapper = new LambdaQueryWrapper<>();
                existingWrapper.in(EduStudent::getStudentNo, allStudentNos)
                        .eq(EduStudent::getDelFlag, 0);
                List<EduStudent> existingStudents = list(existingWrapper);

                Set<String> existingStudentNos = existingStudents.stream()
                        .map(EduStudent::getStudentNo)
                        .collect(Collectors.toSet());
                preloadedData.put("existingStudentNos", existingStudentNos);
            }

            System.out.println("预加载完成 - 班级: " + allClasses.size() +
                    ", 阶段: " + allStages.size() +
                    ", 用户: " + allUsers.size() +
                    ", 历史学生: " + ((Map<?, ?>) preloadedData.getOrDefault("oldStuMap", Collections.emptyMap())).size());

        } catch (Exception e) {
            System.err.println("预加载数据失败: " + e.getMessage());
            e.printStackTrace();
        }

        return preloadedData;
    }

    /*
     * 批量处理班级信息（支持更新和创建）
     * @param classes 班级列表
     * @param preloadedData 预加载的数据
     * @return 班级名称到ID的映射
     */
    @SuppressWarnings("unchecked")
    private Map<String, Integer> batchProcessClassInfoWithUpdate(List<BatchImportDto.ClassImportDto> classes, Map<String, Object> preloadedData) {
        Map<String, Integer> classNameToIdMap = new HashMap<>();
        Map<String, EduClass> classNameMap = (Map<String, EduClass>) preloadedData.getOrDefault("classNameMap", new HashMap<>());
        Map<String, EduStage> stageNameMap = (Map<String, EduStage>) preloadedData.getOrDefault("stageNameMap", new HashMap<>());
        Map<String, SysUser> userNameMap = (Map<String, SysUser>) preloadedData.getOrDefault("userNameMap", new HashMap<>());

        for (BatchImportDto.ClassImportDto classDto : classes) {
            try {
                EduClass existingClass = classNameMap.get(classDto.getClassName());
                if (existingClass != null) {
                    // 更新现有班级信息
                    updateExistingClass(existingClass, classDto, stageNameMap, userNameMap);
                    classNameToIdMap.put(classDto.getClassName(), existingClass.getClassId());
                    System.out.println("更新班级信息: " + classDto.getClassName());
                } else {
                    // 创建新班级
                    Integer classId = createNewClassOptimized(classDto, stageNameMap, userNameMap);
                    classNameToIdMap.put(classDto.getClassName(), classId);
                    System.out.println("创建新班级: " + classDto.getClassName());
                }
            } catch (Exception e) {
                System.err.println("处理班级 " + classDto.getClassName() + " 失败: " + e.getMessage());
                throw new RuntimeException("处理班级信息失败: " + e.getMessage());
            }
        }

        return classNameToIdMap;
    }

    /*
     * 更新现有班级信息
     * @param existingClass 现有班级对象
     * @param classDto 班级导入DTO
     * @param stageNameMap 阶段名称映射
     * @param userNameMap 用户名称映射
     */
    private void updateExistingClass(EduClass existingClass, BatchImportDto.ClassImportDto classDto,
                                     Map<String, EduStage> stageNameMap, Map<String, SysUser> userNameMap) throws Exception {
        boolean hasChanges = false;

        // 1. 更新教室号
        if (classDto.getClassroom() != null && !classDto.getClassroom().trim().isEmpty()) {
            if (!classDto.getClassroom().trim().equals(existingClass.getClassroom())) {
                existingClass.setClassroom(classDto.getClassroom().trim());
                hasChanges = true;
                System.out.println("更新教室号: " + classDto.getClassroom());
            }
        }

        // 2. 更新阶段信息
        if (classDto.getStageName() != null && !classDto.getStageName().trim().isEmpty()) {
            EduStage stage = stageNameMap.get(classDto.getStageName().trim());
            if (stage != null) {
                if (!stage.getStageId().equals(existingClass.getStageId())) {
                    existingClass.setStageId(stage.getStageId());
                    hasChanges = true;
                    System.out.println("更新阶段ID: " + stage.getStageId() + " (" + classDto.getStageName() + ")");
                }
            } else {
                // 创建新阶段
                Integer newStageId = createNewStage(classDto.getStageName().trim(), classDto.getCourseName());
                existingClass.setStageId(newStageId);
                hasChanges = true;
                System.out.println("创建新阶段并更新: " + classDto.getStageName() + " (ID: " + newStageId + ")");
            }
        }

        // 3. 更新讲师信息
        if (classDto.getTeacher() != null && !classDto.getTeacher().trim().isEmpty()) {
            SysUser teacher = findUserByNameAndTypeOptimized(classDto.getTeacher().trim(), 4, userNameMap); // 4-讲师
            if (teacher != null) {
                if (!teacher.getUserId().equals(existingClass.getTeacherId())) {
                    existingClass.setTeacherId(teacher.getUserId());
                    hasChanges = true;
                    System.out.println("更新讲师ID: " + teacher.getUserId() + " (" + classDto.getTeacher() + ")");
                }
            } else {
                // 未找到讲师，设置为null
                if (existingClass.getTeacherId() != null) {
                    existingClass.setTeacherId(null);
                    hasChanges = true;
                    System.out.println("未找到讲师: " + classDto.getTeacher() + "，设置讲师为null");
                }
            }
        } else {
            // 导入数据中没有讲师信息，设置为null
            if (existingClass.getTeacherId() != null) {
                existingClass.setTeacherId(null);
                hasChanges = true;
                System.out.println("导入数据中无讲师信息，设置讲师为null");
            }
        }

        // 4. 更新辅导员信息
        if (classDto.getCounselor() != null && !classDto.getCounselor().trim().isEmpty()) {
            SysUser counselor = findUserByNameAndTypeOptimized(classDto.getCounselor().trim(), 5, userNameMap); // 5-导员
            if (counselor != null) {
                if (!counselor.getUserId().equals(existingClass.getCounselorId())) {
                    existingClass.setCounselorId(counselor.getUserId());
                    hasChanges = true;
                    System.out.println("更新辅导员ID: " + counselor.getUserId() + " (" + classDto.getCounselor() + ")");
                }
            } else {
                // 未找到辅导员，设置为null
                if (existingClass.getCounselorId() != null) {
                    existingClass.setCounselorId(null);
                    hasChanges = true;
                    System.out.println("未找到辅导员: " + classDto.getCounselor() + "，设置辅导员为null");
                }
            }
        } else {
            // 导入数据中没有辅导员信息，设置为null
            if (existingClass.getCounselorId() != null) {
                existingClass.setCounselorId(null);
                hasChanges = true;
                System.out.println("导入数据中无辅导员信息，设置辅导员为null");
            }
        }

        // 5. 如果有变更，更新班级信息
        if (hasChanges) {
            existingClass.setUpdateTime(new Date());
            try {
                Integer currentUserId = UserContext.getCurrentUserId();
                if (currentUserId != null) {
                    existingClass.setUpdateBy(currentUserId);
                }
            } catch (Exception e) {
                System.out.println("无法获取当前用户ID: " + e.getMessage());
            }

            boolean updateResult = eduClassService.updateById(existingClass);
            if (!updateResult) {
                throw new Exception("更新班级信息失败: " + classDto.getClassName());
            }
            System.out.println("班级信息更新成功: " + classDto.getClassName());
        } else {
            System.out.println("班级信息无需更新: " + classDto.getClassName());
        }
    }

    /*
     * 根据姓名和用户类型查找用户（优化版本）
     * @param realName 真实姓名
     * @param userType 用户类型
     * @param userNameMap 用户名称映射
     * @return 用户对象，如果不存在返回null
     */
    private SysUser findUserByNameAndTypeOptimized(String realName, Integer userType, Map<String, SysUser> userNameMap) {
        if (realName == null || realName.trim().isEmpty()) {
            return null;
        }

        String normalizedName = realName.trim();
        
        // 从预加载的映射中查找
        SysUser user = userNameMap.get(normalizedName);
        if (user != null && userType.equals(user.getUserType())) {
            return user;
        }

        // 如果预加载的映射中没有找到，或者用户类型不匹配，则从数据库查询
        LambdaQueryWrapper<SysUser> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.eq(SysUser::getRealName, normalizedName)
                .eq(SysUser::getUserType, userType)
                .eq(SysUser::getDelFlag, 0)
                .eq(SysUser::getStatus, 0); // 正常状态

        return sysUserService.getOne(userWrapper);
    }

    /*
     * 根据姓名和用户类型查找用户
     * @param realName 真实姓名
     * @param userType 用户类型
     * @param userNameMap 用户名称映射
     * @return 用户对象，如果不存在返回null
     */
    private SysUser findUserByNameAndType(String realName, Integer userType, Map<String, SysUser> userNameMap) {
        if (realName == null || realName.trim().isEmpty()) {
            return null;
        }

        String normalizedName = realName.trim();
        
        // 首先从预加载的映射中查找
        SysUser user = userNameMap.get(normalizedName);
        if (user != null && userType.equals(user.getUserType())) {
            return user;
        }

        // 如果预加载的映射中没有找到，或者用户类型不匹配，则从数据库查询
        LambdaQueryWrapper<SysUser> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.eq(SysUser::getRealName, normalizedName)
                .eq(SysUser::getUserType, userType)
                .eq(SysUser::getDelFlag, 0)
                .eq(SysUser::getStatus, 0); // 正常状态

        return sysUserService.getOne(userWrapper);
    }

    /*
     * 创建新阶段
     * @param stageName 阶段名称
     * @param courseName 课程名称
     * @return 阶段ID
     */
    private Integer createNewStage(String stageName, String courseName) throws Exception {
        EduStage newStage = new EduStage();
        newStage.setStageName(stageName);
        newStage.setCourseName(courseName != null ? courseName : "");
        newStage.setParentId(0); // 默认为一级阶段
        newStage.setStageOrder(1); // 默认顺序
        newStage.setStatus(0); // 正常状态
        newStage.setDelFlag(0); // 未删除
        newStage.setCreateTime(new Date());

        try {
            Integer currentUserId = UserContext.getCurrentUserId();
            if (currentUserId != null) {
                newStage.setCreateBy(currentUserId);
            }
        } catch (Exception e) {
            System.out.println("无法获取当前用户ID: " + e.getMessage());
        }

        boolean saveResult = eduStageService.save(newStage);
        if (!saveResult) {
            throw new Exception("创建阶段失败: " + stageName);
        }

        System.out.println("创建新阶段成功: " + stageName + " (ID: " + newStage.getStageId() + ")");
        return newStage.getStageId();
    }

    /*
     * 批量处理班级信息
     * @param classes 班级列表
     * @param preloadedData 预加载的数据
     * @return 班级名称到ID的映射
     */
    @SuppressWarnings("unchecked")
    private Map<String, Integer> batchProcessClassInfo(List<BatchImportDto.ClassImportDto> classes, Map<String, Object> preloadedData) {
        Map<String, Integer> classNameToIdMap = new HashMap<>();
        Map<String, EduClass> classNameMap = (Map<String, EduClass>) preloadedData.getOrDefault("classNameMap", new HashMap<>());
        Map<String, EduStage> stageNameMap = (Map<String, EduStage>) preloadedData.getOrDefault("stageNameMap", new HashMap<>());
        Map<String, SysUser> userNameMap = (Map<String, SysUser>) preloadedData.getOrDefault("userNameMap", new HashMap<>());

        for (BatchImportDto.ClassImportDto classDto : classes) {
            try {
                EduClass existingClass = classNameMap.get(classDto.getClassName());
                if (existingClass != null) {
                    classNameToIdMap.put(classDto.getClassName(), existingClass.getClassId());
                } else {
                    // 创建新班级
                    Integer classId = createNewClassOptimized(classDto, stageNameMap, userNameMap);
                    classNameToIdMap.put(classDto.getClassName(), classId);
                }
            } catch (Exception e) {
                System.err.println("处理班级 " + classDto.getClassName() + " 失败: " + e.getMessage());
                // 使用默认班级ID或抛出异常
                throw new RuntimeException("处理班级信息失败: " + e.getMessage());
            }
        }

        return classNameToIdMap;
    }

    /*
     * 预处理学生信息，准备批量插入
     * 根据学号和姓名从OldStu表查询数据，创建EduStudent对象但不保存
     * @param studentDto 学生导入DTO
     * @param classId 班级ID
     * @param currentUserId 当前用户ID
     * @return EduStudent对象
     */
    private EduStudent prepareStudentInfo(BatchImportDto.StudentImportDto studentDto, Integer classId, Integer currentUserId) throws Exception {
        // 1. 验证学号和姓名
        if (studentDto.getStudentNo() == null || studentDto.getStudentNo().trim().isEmpty()) {
            throw new Exception("学号不能为空");
        }
        if (studentDto.getRealName() == null || studentDto.getRealName().trim().isEmpty()) {
            throw new Exception("姓名不能为空");
        }

        String studentNo = studentDto.getStudentNo().trim();
        String realName = studentDto.getRealName().trim();

        // 2. 检查学号是否已存在于EduStudent表中
        LambdaQueryWrapper<EduStudent> existingWrapper = new LambdaQueryWrapper<>();
        existingWrapper.eq(EduStudent::getStudentNo, studentNo)
                .eq(EduStudent::getDelFlag, 0);

        if (count(existingWrapper) > 0) {
            throw new Exception("学号 " + studentNo + " 已存在");
        }

        // 3. 尝试从OldStu表中查询学生信息（如果存在的话）
        OldStu oldStu = null;
        try {
            LambdaQueryWrapper<OldStu> oldStuWrapper = new LambdaQueryWrapper<>();
            oldStuWrapper.eq(OldStu::getOldstuNo, studentNo)
                    .eq(OldStu::getStuName, realName)
                    .eq(OldStu::getDelFlag, 0);
            oldStu = oldStuService.getOne(oldStuWrapper);
        } catch (Exception e) {
            System.out.println("查询OldStu表失败，将使用默认值创建学生记录: " + e.getMessage());
        }

        // 4. 创建新的学生记录
        EduStudent newStudent = new EduStudent();

        if (oldStu != null) {
            // 如果找到历史数据，使用历史数据
            newStudent.setStudentNo(oldStu.getOldstuNo());
            newStudent.setRealName(oldStu.getStuName());
            newStudent.setGender(oldStu.getGender());
            newStudent.setPhone(oldStu.getPhone());
            newStudent.setEmail(oldStu.getEmail());
            newStudent.setPoints(oldStu.getPoints() != null ? oldStu.getPoints() : 100);
            newStudent.setStatus(oldStu.getStatus() != null ? oldStu.getStatus() : 0);
            newStudent.setRemark(oldStu.getRemark());
        } else {
            // 如果没有找到历史数据，使用导入的数据创建
            newStudent.setStudentNo(studentNo);
            newStudent.setRealName(realName);
            newStudent.setGender(null); // 默认未知性别
            newStudent.setPhone(null);
            newStudent.setEmail(null);
            newStudent.setPoints(100); // 默认100分
            newStudent.setStatus(0); // 默认正常状态
            newStudent.setRemark("批量导入创建");
        }

        newStudent.setClassId(classId); // 使用新的班级ID
        newStudent.setDelFlag(0); // 未删除
        newStudent.setCreateTime(new Date());

        if (currentUserId != null) {
            newStudent.setCreateBy(currentUserId.longValue());
        }

        return newStudent;
    }

    /*
     * 高性能版本的学生信息预处理方法
     * 使用预加载的数据，避免重复查询
     * @param studentDto 学生导入DTO
     * @param classId 班级ID
     * @param currentUserId 当前用户ID
     * @param preloadedData 预加载的数据
     * @return EduStudent对象
     */
    @SuppressWarnings("unchecked")
    private EduStudent prepareStudentInfoOptimized(BatchImportDto.StudentImportDto studentDto, Integer classId,
                                                   Integer currentUserId, Map<String, Object> preloadedData) throws Exception {
        // 1. 验证学号和姓名
        if (studentDto.getStudentNo() == null || studentDto.getStudentNo().trim().isEmpty()) {
            throw new Exception("学号不能为空");
        }
        if (studentDto.getRealName() == null || studentDto.getRealName().trim().isEmpty()) {
            throw new Exception("姓名不能为空");
        }

        String studentNo = studentDto.getStudentNo().trim();
        String realName = studentDto.getRealName().trim();

        // 2. 检查学号是否已存在（使用预加载的数据）
        Set<String> existingStudentNos = (Set<String>) preloadedData.getOrDefault("existingStudentNos", new HashSet<>());
        if (existingStudentNos.contains(studentNo)) {
            throw new Exception("学号 " + studentNo + " 已存在");
        }

        // 3. 从预加载的历史数据中查询学生信息
        Map<String, OldStu> oldStuMap = (Map<String, OldStu>) preloadedData.getOrDefault("oldStuMap", new HashMap<>());
        OldStu oldStu = oldStuMap.get(studentNo);

        // 4. 创建学生对象
        EduStudent student = new EduStudent();
        student.setStudentNo(studentNo);
        student.setRealName(realName);
        student.setClassId(classId);

        if (oldStu != null) {
            // 从历史数据恢复信息
            student.setGender(oldStu.getGender());
            student.setPhone(oldStu.getPhone());
            student.setEmail(oldStu.getEmail());
            student.setPoints(oldStu.getPoints() != null ? oldStu.getPoints() : 100);
            student.setStatus(oldStu.getStatus() != null ? oldStu.getStatus() : 0);
            student.setRemark(oldStu.getRemark());
            student.setRole(oldStu.getRole());
            student.setPassword(oldStu.getPassword());
        } else {
            // 使用默认值
            student.setGender(null);
            student.setPhone(null);
            student.setEmail(null);
            student.setPoints(100);
            student.setStatus(0);
            student.setRemark(null);
            student.setRole(1);
            student.setPassword(null);
        }

        // 5. 设置系统字段
        Date currentTime = new Date();
        student.setCreateTime(currentTime);
        student.setDelFlag(0);

        if (currentUserId != null) {
            student.setCreateBy(currentUserId.longValue());
            student.setUpdateBy(currentUserId);
        }

        return student;
    }

    /*
     * 优化版本的班级创建方法
     * 使用预加载的数据，避免重复查询
     * @param classDto 班级DTO
     * @param stageNameMap 阶段名称映射
     * @param userNameMap 用户名称映射
     * @return 班级ID
     */
    private Integer createNewClassOptimized(BatchImportDto.ClassImportDto classDto,
                                            Map<String, EduStage> stageNameMap,
                                            Map<String, SysUser> userNameMap) throws Exception {
        EduClass newClass = new EduClass();
        newClass.setClassName(classDto.getClassName());
        
        // 设置教室号
        if (classDto.getClassroom() != null && !classDto.getClassroom().trim().isEmpty()) {
            newClass.setClassroom(classDto.getClassroom().trim());
        }

        // 处理阶段信息
        if (classDto.getStageName() != null && !classDto.getStageName().trim().isEmpty()) {
            EduStage stage = stageNameMap.get(classDto.getStageName().trim());
            if (stage != null) {
                newClass.setStageId(stage.getStageId());
                System.out.println("使用现有阶段: " + classDto.getStageName() + " (ID: " + stage.getStageId() + ")");
            } else {
                // 创建新阶段
                Integer newStageId = createNewStage(classDto.getStageName().trim(), classDto.getCourseName());
                newClass.setStageId(newStageId);
                System.out.println("创建新阶段: " + classDto.getStageName() + " (ID: " + newStageId + ")");
            }
        } else {
            // 使用默认阶段
            newClass.setStageId(1);
            System.out.println("使用默认阶段ID: 1");
        }

        // 处理讲师信息
        if (classDto.getTeacher() != null && !classDto.getTeacher().trim().isEmpty()) {
            SysUser teacher = findUserByNameAndTypeOptimized(classDto.getTeacher().trim(), 4, userNameMap); // 4-讲师
            if (teacher != null) {
                newClass.setTeacherId(teacher.getUserId());
                System.out.println("设置讲师: " + classDto.getTeacher() + " (ID: " + teacher.getUserId() + ")");
            } else {
                newClass.setTeacherId(null);
                System.out.println("未找到讲师: " + classDto.getTeacher() + "，设置讲师为null");
            }
        } else {
            newClass.setTeacherId(null);
            System.out.println("导入数据中无讲师信息，设置讲师为null");
        }

        // 处理辅导员信息
        if (classDto.getCounselor() != null && !classDto.getCounselor().trim().isEmpty()) {
            SysUser counselor = findUserByNameAndTypeOptimized(classDto.getCounselor().trim(), 5, userNameMap); // 5-导员
            if (counselor != null) {
                newClass.setCounselorId(counselor.getUserId());
                System.out.println("设置辅导员: " + classDto.getCounselor() + " (ID: " + counselor.getUserId() + ")");
            } else {
                newClass.setCounselorId(null);
                System.out.println("未找到辅导员: " + classDto.getCounselor() + "，设置辅导员为null");
            }
        } else {
            newClass.setCounselorId(null);
            System.out.println("导入数据中无辅导员信息，设置辅导员为null");
        }

        // 设置默认值
        newClass.setStatus(0); // 正常状态
        newClass.setCreateTime(new Date());
        newClass.setDelFlag(0);

        // 设置创建者
        try {
            Integer currentUserId = UserContext.getCurrentUserId();
            if (currentUserId != null) {
                newClass.setCreateBy(currentUserId);
            }
        } catch (Exception e) {
            System.out.println("无法获取当前用户ID: " + e.getMessage());
        }

        // 保存班级
        boolean saved = eduClassService.save(newClass);
        if (!saved) {
            throw new Exception("创建班级失败: " + classDto.getClassName());
        }

        System.out.println("创建班级成功: " + classDto.getClassName() + " (ID: " + newClass.getClassId() + ")");
        return newClass.getClassId();
    }

    /*
     * 为批量导入重置表的ID自增
     * 在批量导入前执行，简化版本
     */
    private void resetTableAutoIncrementForImport() {
        try {
            System.out.println("开始重置表ID自增（批量导入前）...");

            // 简化配置：只重置学生表
            Map<String, Integer> tableConfigs = new HashMap<>();
            tableConfigs.put("edu_student", 1);

            // 执行重置
            resetMultipleTablesAutoIncrement(tableConfigs);

        } catch (Exception e) {
            System.err.println("批量导入前重置表ID自增失败: " + e.getMessage());
            // 不抛出异常，允许导入继续进行
        }
    }

    /*
     * 重置表的ID自增（通用方法）
     * @param resetStudentTable 是否重置学生表
     * @param resetClassTable 是否重置班级表
     * @param resetStageTable 是否重置阶段表
     * @param resetOldStuTable 是否重置历史学生表
     */
    @Override
    public void resetTableAutoIncrement(boolean resetStudentTable,
                                        boolean resetClassTable,
                                        boolean resetStageTable,
                                        boolean resetOldStuTable) {
        try {
            System.out.println("开始自定义重置表ID自增...");

            Map<String, Integer> tableConfigs = new LinkedHashMap<>();

            if (resetStudentTable) {
                tableConfigs.put("edu_student", 1);
            }
            if (resetClassTable) {
                tableConfigs.put("edu_class", 1);
            }
            if (resetStageTable) {
                tableConfigs.put("edu_stage", 1);
            }
            if (resetOldStuTable) {
                tableConfigs.put("old_stu", 1);
            }

            if (!tableConfigs.isEmpty()) {
                resetMultipleTablesAutoIncrement(tableConfigs);
            } else {
                System.out.println("未选择任何表进行ID自增重置");
            }

        } catch (Exception e) {
            System.err.println("自定义重置表ID自增失败: " + e.getMessage());
            throw new RuntimeException("重置表ID自增失败: " + e.getMessage());
        }
    }

    /*
     * 重置指定表的ID自增
     * @param tableName 表名
     * @param startValue 起始值，默认为1
     */
    private void resetTableAutoIncrement(String tableName, int startValue) {
        try {
            String sql = String.format("ALTER TABLE %s AUTO_INCREMENT = %d", tableName, startValue);
            jdbcTemplate.execute(sql);
            System.out.println(String.format("表 %s 的ID自增已重置为 %d", tableName, startValue));
        } catch (Exception e) {
            System.err.println(String.format("重置表 %s ID自增失败: %s", tableName, e.getMessage()));
        }
    }

    /*
     * 批量重置多个表的ID自增
     * @param tableConfigs 表配置映射，key为表名，value为起始值
     */
    private void resetMultipleTablesAutoIncrement(Map<String, Integer> tableConfigs) {
        System.out.println("开始批量重置表ID自增...");

        for (Map.Entry<String, Integer> entry : tableConfigs.entrySet()) {
            resetTableAutoIncrement(entry.getKey(), entry.getValue());
        }

        System.out.println("批量重置表ID自增完成");
    }


    /*
     * 处理班级信息
     * 根据导入信息创建或更新班级，包括阶段、讲师、辅导员等信息
     * @param classDto 班级导入DTO
     * @return 班级ID
     */
    private Integer processClassInfo(BatchImportDto.ClassImportDto classDto) throws Exception {
        // 1. 尝试查找或创建阶段信息
        Integer stageId = null;
        try {
            stageId = findOrCreateStage(classDto.getStageName(), classDto.getCourseName());
        } catch (Exception e) {
            System.out.println("创建阶段信息失败，使用默认值: " + e.getMessage());
        }

        // 2. 尝试查找讲师和辅导员ID（如果失败则使用null）
        Integer teacherId = null;
        Integer counselorId = null;
        try {
            teacherId = findUserByRealName(classDto.getTeacher(), 1); // 1-讲师
            counselorId = findUserByRealName(classDto.getCounselor(), 2); // 2-辅导员
        } catch (Exception e) {
            System.out.println("查找用户信息失败，将使用空值: " + e.getMessage());
        }

        // 3. 查找或创建班级
        EduClass existingClass = findClassByName(classDto.getClassName());

        if (existingClass != null) {
            // 更新现有班级信息
            existingClass.setStageId(stageId);
            existingClass.setTeacherId(teacherId);
            existingClass.setCounselorId(counselorId);
            existingClass.setClassroom(classDto.getClassroom());
            existingClass.setUpdateTime(new Date());

            try {
                Integer currentUserId = UserContext.getCurrentUserId();
                if (currentUserId != null) {
                    existingClass.setUpdateBy(currentUserId);
                }
            } catch (Exception e) {
                // 忽略获取用户ID失败的情况
            }

            eduClassService.updateById(existingClass);
            return existingClass.getClassId();
        } else {
            // 创建新班级
            EduClass newClass = new EduClass();
            newClass.setClassName(classDto.getClassName());
            newClass.setStageId(stageId);
            newClass.setTeacherId(teacherId);
            newClass.setCounselorId(counselorId);
            newClass.setClassroom(classDto.getClassroom());
            newClass.setStatus(0); // 正常状态
            newClass.setDelFlag(0); // 未删除
            newClass.setCreateTime(new Date());

            try {
                Integer currentUserId = UserContext.getCurrentUserId();
                if (currentUserId != null) {
                    newClass.setCreateBy(currentUserId);
                }
            } catch (Exception e) {
                // 忽略获取用户ID失败的情况
            }

            eduClassService.save(newClass);
            return newClass.getClassId();
        }
    }

    /*
     * 查找或创建阶段信息
     * @param stageName 阶段名称
     * @param courseName 课程名称
     * @return 阶段ID
     */
    private Integer findOrCreateStage(String stageName, String courseName) throws Exception {
        // 查找现有阶段
        LambdaQueryWrapper<EduStage> stageWrapper = new LambdaQueryWrapper<>();
        stageWrapper.eq(EduStage::getStageName, stageName)
                .eq(EduStage::getCourseName, courseName);

        EduStage existingStage = eduStageService.getOne(stageWrapper);

        if (existingStage != null) {
            return existingStage.getStageId();
        }

        // 创建新阶段
        EduStage newStage = new EduStage();
        newStage.setStageName(stageName);
        newStage.setCourseName(courseName);
        newStage.setParentId(0); // 默认为一级阶段
        newStage.setStageOrder(1); // 默认顺序
        newStage.setStatus(0); // 正常状态
        newStage.setDelFlag(0); // 未删除
        newStage.setCreateTime(new Date());

        try {
            Integer currentUserId = UserContext.getCurrentUserId();
            if (currentUserId != null) {
                newStage.setCreateBy(currentUserId);
            }
        } catch (Exception e) {
            // 忽略获取用户ID失败的情况
        }

        eduStageService.save(newStage);
        return newStage.getStageId();
    }

    /*
     * 根据真实姓名查找用户ID
     * @param realName 真实姓名
     * @param userType 用户类型（1-讲师，2-辅导员）
     * @return 用户ID，如果不存在返回null
     */
    private Integer findUserByRealName(String realName, Integer userType) {
        if (realName == null || realName.trim().isEmpty()) {
            return null;
        }

        LambdaQueryWrapper<SysUser> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.eq(SysUser::getRealName, realName.trim())
                .eq(SysUser::getUserType, userType)
                .eq(SysUser::getDelFlag, 0)
                .eq(SysUser::getStatus, 0); // 正常状态

        SysUser user = sysUserService.getOne(userWrapper);
        return user != null ? user.getUserId() : null;
    }

    /*
     * 根据班级名称查找班级
     * @param className 班级名称
     * @return 班级对象，如果不存在返回null
     */
    private EduClass findClassByName(String className) {
        if (className == null || className.trim().isEmpty()) {
            return null;
        }

        LambdaQueryWrapper<EduClass> classWrapper = new LambdaQueryWrapper<>();
        classWrapper.eq(EduClass::getClassName, className.trim())
                .eq(EduClass::getDelFlag, 0);

        return eduClassService.getOne(classWrapper);
    }

    /**
     * 处理单个学生信息
     * 根据学号和姓名从OldStu表查询数据，然后添加到EduStudent表
     *
     * @param studentDto    学生导入DTO
     * @param classId       班级ID
     * @param currentUserId 当前用户ID
     */
    private void processStudentInfo(BatchImportDto.StudentImportDto studentDto, Integer classId, Integer currentUserId) throws Exception {
        // 1. 验证学号和姓名
        if (studentDto.getStudentNo() == null || studentDto.getStudentNo().trim().isEmpty()) {
            throw new Exception("学号不能为空");
        }
        if (studentDto.getRealName() == null || studentDto.getRealName().trim().isEmpty()) {
            throw new Exception("姓名不能为空");
        }

        String studentNo = studentDto.getStudentNo().trim();
        String realName = studentDto.getRealName().trim();

        // 2. 检查学号是否已存在于EduStudent表中
        LambdaQueryWrapper<EduStudent> existingWrapper = new LambdaQueryWrapper<>();
        existingWrapper.eq(EduStudent::getStudentNo, studentNo)
                .eq(EduStudent::getDelFlag, 0);

        if (count(existingWrapper) > 0) {
            throw new Exception("学号 " + studentNo + " 已存在");
        }

        // 3. 从OldStu表中查询学生信息
        LambdaQueryWrapper<OldStu> oldStuWrapper = new LambdaQueryWrapper<>();
        oldStuWrapper.eq(OldStu::getOldstuNo, studentNo)
                .eq(OldStu::getStuName, realName)
                .eq(OldStu::getDelFlag, 0);

        OldStu oldStu = oldStuService.getOne(oldStuWrapper);

        if (oldStu == null) {
            throw new Exception("在历史数据中未找到学号为 " + studentNo + "，姓名为 " + realName + " 的学生信息");
        }

        // 4. 创建新的学生记录
        EduStudent newStudent = new EduStudent();
        newStudent.setStudentNo(oldStu.getOldstuNo());
        newStudent.setRealName(oldStu.getStuName());

        // 转换性别
        if ("男".equals(oldStu.getGender())) {
            newStudent.setGender(1);
        } else if ("女".equals(oldStu.getGender())) {
            newStudent.setGender(0);
        } else {
            newStudent.setGender(null); // 未知性别
        }

        newStudent.setPhone(oldStu.getPhone());
        newStudent.setEmail(oldStu.getEmail());
        newStudent.setClassId(classId); // 使用新的班级ID
        newStudent.setPoints(oldStu.getPoints() != null ? oldStu.getPoints() : 100); // 默认100分
        newStudent.setStatus(oldStu.getStatus() != null ? oldStu.getStatus() : 0); // 默认正常状态
        newStudent.setRemark(oldStu.getRemark());
        newStudent.setDelFlag(0); // 未删除
        newStudent.setCreateTime(new Date());

        if (currentUserId != null) {
            newStudent.setCreateBy(currentUserId.longValue());
        }

        // 5. 保存学生信息
        boolean saveResult = save(newStudent);
        if (!saveResult) {
            throw new Exception("保存学生信息失败");
        }
    }

    /**
     * 验证导入数据
     *
     * @param importData 导入数据
     * @return 验证结果
     */
    @Override
    public List<BatchImportDto.ClassImportDto> validateImportData(BatchImportDto importData) throws Exception {
        List<BatchImportDto.ClassImportDto> validatedClasses = new ArrayList<>();

        for (BatchImportDto.ClassImportDto classDto : importData.getClasses()) {
            BatchImportDto.ClassImportDto validatedClass = new BatchImportDto.ClassImportDto();
            validatedClass.setClassName(classDto.getClassName());
            validatedClass.setCounselor(classDto.getCounselor());
            validatedClass.setTeacher(classDto.getTeacher());
            validatedClass.setClassroom(classDto.getClassroom());
            validatedClass.setStageName(classDto.getStageName());
            validatedClass.setCourseName(classDto.getCourseName());

            List<BatchImportDto.StudentImportDto> validatedStudents = new ArrayList<>();

            for (BatchImportDto.StudentImportDto studentDto : classDto.getStudents()) {
                BatchImportDto.StudentImportDto validatedStudent = new BatchImportDto.StudentImportDto();
                validatedStudent.setStudentNo(studentDto.getStudentNo());
                validatedStudent.setRealName(studentDto.getRealName());

                // 验证学生数据
                boolean isValid = validateStudentData(studentDto);
                // 这里可以添加更多验证逻辑，比如设置错误信息等

                validatedStudents.add(validatedStudent);
            }

            validatedClass.setStudents(validatedStudents);
            validatedClasses.add(validatedClass);
        }

        return validatedClasses;
    }

    /**
     * 验证单个学生数据
     *
     * @param studentDto 学生DTO
     * @return 是否有效
     */
    private boolean validateStudentData(BatchImportDto.StudentImportDto studentDto) {
        // 1. 验证学号格式
        if (studentDto.getStudentNo() == null || studentDto.getStudentNo().trim().isEmpty()) {
            return false;
        }

        String studentNo = studentDto.getStudentNo().trim();
        if (studentNo.length() < 8 || !studentNo.matches("\\d+")) {
            return false;
        }

        // 2. 验证姓名
        if (studentDto.getRealName() == null || studentDto.getRealName().trim().isEmpty()) {
            return false;
        }

        // 3. 检查是否已存在
        LambdaQueryWrapper<EduStudent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EduStudent::getStudentNo, studentNo)
                .eq(EduStudent::getDelFlag, 0);

        if (count(wrapper) > 0) {
            return false; // 学号已存在
        }

        // 4. 检查在OldStu表中是否存在
        LambdaQueryWrapper<OldStu> oldStuWrapper = new LambdaQueryWrapper<>();
        oldStuWrapper.eq(OldStu::getOldstuNo, studentNo)
                .eq(OldStu::getStuName, studentDto.getRealName().trim())
                .eq(OldStu::getDelFlag, 0);

        return oldStuService.count(oldStuWrapper) > 0;
    }
}
