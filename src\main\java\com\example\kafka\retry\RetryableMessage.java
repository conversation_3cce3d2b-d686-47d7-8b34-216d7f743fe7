package com.example.kafka.retry;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 可重试消息包装类
 */
public class RetryableMessage<T> {
    
    @JsonProperty("originalTopic")
    private String originalTopic;
    
    @JsonProperty("originalPartition")
    private Integer originalPartition;
    
    @JsonProperty("originalOffset")
    private Long originalOffset;
    
    @JsonProperty("payload")
    private T payload;
    
    @JsonProperty("retryCount")
    private int retryCount = 0;
    
    @JsonProperty("maxRetries")
    private int maxRetries = 3;
    
    @JsonProperty("firstFailureTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime firstFailureTime;
    
    @JsonProperty("lastFailureTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastFailureTime;
    
    @JsonProperty("errorMessage")
    private String errorMessage;
    
    @JsonProperty("errorClass")
    private String errorClass;
    
    @JsonProperty("headers")
    private Map<String, String> headers;
    
    @JsonProperty("retryDelayMs")
    private long retryDelayMs = 1000; // 默认1秒延迟
    
    public RetryableMessage() {}
    
    public RetryableMessage(String originalTopic, T payload) {
        this.originalTopic = originalTopic;
        this.payload = payload;
        this.firstFailureTime = LocalDateTime.now();
    }
    
    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.retryCount++;
        this.lastFailureTime = LocalDateTime.now();
        if (this.firstFailureTime == null) {
            this.firstFailureTime = LocalDateTime.now();
        }
    }
    
    /**
     * 是否可以重试
     */
    public boolean canRetry() {
        return retryCount < maxRetries;
    }
    
    /**
     * 是否已达到最大重试次数
     */
    public boolean isMaxRetriesReached() {
        return retryCount >= maxRetries;
    }
    
    /**
     * 计算下次重试延迟时间（指数退避）
     */
    public long calculateNextRetryDelay() {
        return retryDelayMs * (long) Math.pow(2, retryCount);
    }
    
    // Getters and Setters
    public String getOriginalTopic() {
        return originalTopic;
    }
    
    public void setOriginalTopic(String originalTopic) {
        this.originalTopic = originalTopic;
    }
    
    public Integer getOriginalPartition() {
        return originalPartition;
    }
    
    public void setOriginalPartition(Integer originalPartition) {
        this.originalPartition = originalPartition;
    }
    
    public Long getOriginalOffset() {
        return originalOffset;
    }
    
    public void setOriginalOffset(Long originalOffset) {
        this.originalOffset = originalOffset;
    }
    
    public T getPayload() {
        return payload;
    }
    
    public void setPayload(T payload) {
        this.payload = payload;
    }
    
    public int getRetryCount() {
        return retryCount;
    }
    
    public void setRetryCount(int retryCount) {
        this.retryCount = retryCount;
    }
    
    public int getMaxRetries() {
        return maxRetries;
    }
    
    public void setMaxRetries(int maxRetries) {
        this.maxRetries = maxRetries;
    }
    
    public LocalDateTime getFirstFailureTime() {
        return firstFailureTime;
    }
    
    public void setFirstFailureTime(LocalDateTime firstFailureTime) {
        this.firstFailureTime = firstFailureTime;
    }
    
    public LocalDateTime getLastFailureTime() {
        return lastFailureTime;
    }
    
    public void setLastFailureTime(LocalDateTime lastFailureTime) {
        this.lastFailureTime = lastFailureTime;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public String getErrorClass() {
        return errorClass;
    }
    
    public void setErrorClass(String errorClass) {
        this.errorClass = errorClass;
    }
    
    public Map<String, String> getHeaders() {
        return headers;
    }
    
    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }
    
    public long getRetryDelayMs() {
        return retryDelayMs;
    }
    
    public void setRetryDelayMs(long retryDelayMs) {
        this.retryDelayMs = retryDelayMs;
    }
    
    @Override
    public String toString() {
        return "RetryableMessage{" +
                "originalTopic='" + originalTopic + '\'' +
                ", retryCount=" + retryCount +
                ", maxRetries=" + maxRetries +
                ", errorMessage='" + errorMessage + '\'' +
                ", canRetry=" + canRetry() +
                '}';
    }
}
