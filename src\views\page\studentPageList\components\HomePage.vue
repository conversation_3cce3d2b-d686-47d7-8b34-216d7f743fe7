<template>
  <div class="home-page">
    <!-- 欢迎横幅 -->
    <el-card class="welcome-banner" shadow="hover">
      <div class="banner-content">
        <div class="welcome-text">
          <h1>欢迎回来，{{ studentInfo.realName }}！</h1>
          <p>学号：{{ studentInfo.studentNo }} | 班级：{{ studentInfo.className }}</p>
        </div>
        <div class="welcome-stats">
          <div class="stat-item">
            <el-icon class="stat-icon"><Trophy /></el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ studentInfo.points }}</div>
              <div class="stat-label">当前积分</div>
            </div>
          </div>
          <div class="stat-item">
            <el-icon class="stat-icon"><Star /></el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ rank.rank }}</div>
              <div class="stat-label">班级排名</div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <el-row :gutter="20" class="main-content">
      <!-- 左侧内容 -->
      <el-col :span="16">
        <!-- 排行榜模块 -->
        <el-card class="ranking-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><TrendCharts /></el-icon>
              <span>{{ showTopRanking ? '积分前十名' : '积分后十名' }}</span>
              <el-tag type="info" size="small">实时更新</el-tag>
              <el-button
                  type="primary"
                  size="small"
                  @click="toggleRanking"
                  class="toggle-ranking-btn"
              >
                {{ toggleButtonText }}
              </el-button>
            </div>
          </template>
          <div class="ranking-content">

            <div class="ranking-list">
              <div
                v-for="(student, index) in currentStudents"
                :key="student.id"
                class="ranking-item"
                :class="{
                  'current-student': student.id === studentInfo.id,
                  'bottom-ranking': !showTopRanking
                }"
              >
                <div class="rank-number">
                  <el-icon v-if="showTopRanking && index < 3" class="medal-icon" :class="`medal-${index + 1}`">
                    <Medal />
                  </el-icon>
                  <span v-else class="rank-text">
                    {{ (index + 1)  }}
                  </span>
                </div>
<!--                <el-avatar :size="40" :src="student.avatar">{{ student.name.charAt(0) }}</el-avatar>-->
                <div class="student-info">
                  <div class="student-name">{{ student.realName }}</div>
                  <div class="student-class">{{ student.className }}</div>
                </div>
                <div class="student-points">{{ student.points }}分</div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 班级积分统计图表 -->
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><TrendCharts /></el-icon>
              <span>班级积分统计</span>
              <el-button type="primary" size="small" @click="refreshChart">刷新</el-button>
            </div>
          </template>
          <div class="chart-content">
            <div ref="chartRef" class="class-chart" v-loading="chartLoading"></div>
          </div>
        </el-card>

      </el-col>

      <!-- 右侧内容 -->
      <el-col :span="8">
        <!-- 通知公告模块 -->
        <el-card class="notice-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Bell /></el-icon>
              <span>通知公告</span>
<!--              <el-link type="primary" :underline="false" @click="viewAllNotices">查看全部</el-link>-->
            </div>
          </template>
          <div class="notice-content" ref="noticeContent">
            <div
              v-for="notice in activities"
              :key="notice.id"
              class="notice-item"
              @click="viewNoticeDetail(notice)"
            >
              <!-- 可报名提示 -->
              <div v-if="isActivityAvailable(notice)" class="signup-badge">
                <el-icon class="badge-icon"><Check /></el-icon>
                <span>可报名</span>
              </div>
              
              <!-- 活动状态提示 -->
              <div v-else-if="getActivityStatus(notice)" class="status-badge" :class="getActivityStatus(notice).type">
                <el-icon class="badge-icon" v-if="getActivityStatus(notice).icon">
                  <component :is="getActivityStatus(notice).icon" />
                </el-icon>
                <span>{{ getActivityStatus(notice).text }}</span>
              </div>
              
              <div class="notice-title">{{ notice.activityName }}</div>
              <div class="notice-summary">{{ notice.activityText }}</div>
            </div>
            <!-- 滚动提示 -->
            <div v-if="showScrollHint" class="scroll-hint">
              <el-icon><ArrowDown /></el-icon>
              <span>向下滚动查看更多</span>
            </div>
          </div>
        </el-card>

      </el-col>
    </el-row>
  </div>
  <el-dialog
      v-model="dialogVisible"
      title="活动详情"
      width="700px"
      :close-on-click-modal="false"
      class="activity-detail-dialog"
      top="5vh"
  >
    <template #header>
      <div class="dialog-header">
        <el-icon class="header-icon"><Bell /></el-icon>
        <span class="dialog-title">活动详情</span>
      </div>
    </template>

    <div class="activity-detail-content">
      <div class="activity-form">
        <!-- 顶部：活动名称 -->
        <div class="activity-header">
          <div class="activity-title">
            <el-icon class="title-icon"><Star /></el-icon>
            <span>{{ xq.activityName || '暂无活动名称' }}</span>
          </div>
        </div>

        <!-- 品字形布局 -->
        <div class="activity-layout">
          <!-- 左上：活动内容 -->
          <div class="activity-section content-section">
            <div class="section-header">
              <el-icon class="section-icon"><Document /></el-icon>
              <span class="section-title">活动内容</span>
            </div>
            <div class="section-content">
              <p>{{ xq.activityText || '暂无活动内容' }}</p>
            </div>
          </div>

          <!-- 右上：时间信息 -->
          <div class="activity-section time-section">
            <div class="section-header">
              <el-icon class="section-icon"><Operation /></el-icon>
              <span class="section-title">时间安排</span>
            </div>
            <div class="time-info">
              <div class="time-item">
                <span class="time-label">开始时间</span>
                <span class="time-value">{{ xq.startTime || '暂无' }}</span>
              </div>
              <div class="time-item">
                <span class="time-label">结束时间</span>
                <span class="time-value">{{ xq.stopTime || '暂无' }}</span>
              </div>
            </div>
          </div>

          <!-- 左下：参与信息 -->
          <div class="activity-section participants-section">
            <div class="section-header">
              <el-icon class="section-icon"><User /></el-icon>
              <span class="section-title">参与信息</span>
            </div>
            <div class="participants-info">
              <div class="participants-count">
                <span class="count-number">{{ xq.number || 0 }}</span>
                <span class="count-unit">人</span>
              </div>
              <el-tag 
                v-if="xq.number" 
                :type="xq.number > 10 ? 'success' : xq.number > 5 ? 'warning' : 'danger'"
                size="large"
                class="participants-tag"
              >
                {{ xq.number > 10 ? '热门活动' : xq.number > 5 ? '适中规模' : '小规模' }}
              </el-tag>
            </div>
          </div>

          <!-- 右下：活动图片 -->
          <div class="activity-section image-section">
            <div class="section-header">
              <el-icon class="section-icon"><Picture /></el-icon>
              <span class="section-title">活动图片</span>
            </div>
            <div class="image-container">
              <el-image
                v-if="xq.image"
                :src="xq.image"
                class="activity-image"
                :preview-src-list="[xq.image]"
                fit="cover"
              >
                <template #error>
                  <div class="image-error">
                    <el-icon><Document /></el-icon>
                    <span>图片加载失败</span>
                  </div>
                </template>
              </el-image>
              <div v-else class="no-image">
                <el-icon><Document /></el-icon>
                <span>暂无图片</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false" size="large">
          关闭
        </el-button>
        <el-button type="primary" size="large" @click="onSubmit">
          <el-icon><Plus /></el-icon>
          立即报名
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import {ref, computed, onUnmounted, onMounted, nextTick} from 'vue'
import {
  Trophy, Star, TrendCharts, Medal, Bell, Operation,
  Document, Plus, Search, Setting, User, Picture, ArrowDown, Check,
} from '@element-plus/icons-vue'
import {
  avgPoints, getClassRank,
  queryBottomTenStudentsByClass,
  queryTopTenStudentsByClass
} from "@/api/system/student.js";
import {findActivity} from "@/api/system/activity.js";
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'
import {signUpActivity} from "@/api/system/activityApplication.js";
const studentInfo = JSON.parse(localStorage.getItem("studentList"));
const sign=ref({
  name:studentInfo.realName,
  phone:studentInfo.phone,
  className:studentInfo.className,
  activityId:""
})
console.log( sign.value.activityId)
function onSubmit() {
  signUpActivity(sign.value).then(res=>{
    ElMessage.success(res.data.message)
  })
}
// 获取router传过来的学生信息


const rank=ref([])

getClassRank(studentInfo.classId).then(res=>{
  rank.value=res.data.data
})


const selectedHonorCategory = ref('all')

// 荣誉数据
const honorList = ref([
  {
    id: 1,
    title: '三好学生',
    description: '学习成绩优异，品德良好',
    date: '2024-12-15',
    category: 'study',
    iconClass: 'honor-gold'
  },
  {
    id: 2,
    title: '优秀班干部',
    description: '积极参与班级管理工作',
    date: '2024-11-20',
    category: 'activity',
    iconClass: 'honor-silver'
  },
  {
    id: 3,
    title: '社会实践先进个人',
    description: '暑期社会实践表现突出',
    date: '2024-09-10',
    category: 'practice',
    iconClass: 'honor-bronze'
  }
])


// 图表相关
const chartRef = ref(null)
const chartLoading = ref(false)
let chartInstance = null

// 班级积分数据
const classData = ref({
  classNames: [],
  avgPoints: [],
  studentNums: []
})

// 初始化图表
const initChart = () => {
  if (chartRef.value) {
    if (chartInstance) {
      chartInstance.dispose()
    }
    chartInstance = echarts.init(chartRef.value)

    const option = {
      title: {
        text: '各班级平均积分对比',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          const dataIndex = params[0].dataIndex
          const className = classData.value.classNames[dataIndex]
          const avgPoint = classData.value.avgPoints[dataIndex]
          const studentNum = classData.value.studentNums[dataIndex]
          return `${className}<br/>平均积分: ${avgPoint.toFixed(1)}<br/>学生人数: ${studentNum}人`
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: classData.value.classNames,
        axisTick: { alignWithLabel: true },
        axisLabel: {
          interval: 0,
          rotate: 45,
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        name: '平均积分',
        nameTextStyle: {
          fontSize: 12
        }
      },
      series: [
        {
          name: '平均积分',
          type: 'bar',
          barWidth: '60%',
          data: classData.value.avgPoints,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#667eea' },
              { offset: 1, color: '#764ba2' }
            ])
          },
          label: {
            show: true,
            position: 'top',
            formatter: function(params) {
              return params.value.toFixed(1)
            },
            fontSize: 10
          }
        }
      ]
    }
    chartInstance.setOption(option)
  }
}

// 获取班级积分数据
const fetchClassData = async () => {
  chartLoading.value = true
  try {
    const res = await avgPoints()
    if (res.data && res.data.code === 200) {
      const data = res.data.data || []
      classData.value.classNames = data.map(item => item.className)
      classData.value.avgPoints = data.map(item => item.avgPoints || 0)
      classData.value.studentNums = data.map(item => item.studentNum || 0)
      nextTick(() => initChart())
    } else {
      console.warn('获取班级积分数据失败:', res)
      ElMessage.warning('获取班级积分数据失败')
    }
  } catch (error) {
    console.error('获取班级积分数据出错:', error)
    ElMessage.error('获取班级积分数据失败')
  } finally {
    chartLoading.value = false
  }
}

// 刷新图表
const refreshChart = () => {
  fetchClassData()
}

// 图表响应式处理
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

const viewNoticeDetail = (notice) => {
  sign.value.activityId=notice.id
  xq.value=notice
  dialogVisible.value = true;
  console.log('查看通知详情:', notice)
}
const dialogVisible = ref(false)
const xq=ref([])
const handleQuickAction = (actionKey) => {
  console.log('查看通知详情:', actionKey)
}
const intervalIds=ref([]);
const topStudents = ref([]);
//获取本班前十名
const findTopStudents = () => {
  queryTopTenStudentsByClass(studentInfo.classId).then((res) => {
    // 统一字段名
    if (res.data && res.data.data && res.data.data.length > 0) {
      topStudents.value = res.data.data;
    } else {
      console.log('API无数据，使用模拟数据');
    }
  })

  const id = setInterval(() => {
    queryTopTenStudentsByClass(studentInfo.classId).then((res) => {
      if (res.data && res.data.data && res.data.data.length > 0) {
        topStudents.value = res.data.data;
      }
    }).catch(error => {
      console.error('定时获取前十名失败:', error);
    });
  }, 5000);
  intervalIds.value.push(id);
};
const showTopRanking = ref(true); // true显示前十名，false显示后十名
const bottomStudents = ref([]); // 存储后十名数据

const toggleButtonText = computed(() => {
  return showTopRanking.value ? '查看后十名' : '查看前十名';
});

const toggleRanking = () => {
  showTopRanking.value = !showTopRanking.value;
  console.log('切换排名显示:', showTopRanking.value ? '前十名' : '后十名');

  // 切换到后十名时，如果还没有数据就获取
  if (!showTopRanking.value && bottomStudents.value.length === 0) {
    findBottomStudents();
  }
};

// 计算当前显示的学生列表
const currentStudents = computed(() => {
  return showTopRanking.value ? topStudents.value : bottomStudents.value;
});
//获取本班后十名
const findBottomStudents = () => {
  queryBottomTenStudentsByClass(studentInfo.classId).then((res) => {
    // 存储到后十名数据中
    bottomStudents.value = res.data.data;
    console.log('获取后十名数据:', bottomStudents.value);
  })

  const id = setInterval(() => {
    queryBottomTenStudentsByClass(studentInfo.classId).then((res) => {
      if (res.data && res.data.data && res.data.data.length > 0) {
        bottomStudents.value = res.data.data;
      }
    }).catch(error => {
      console.error('定时获取后十名失败:', error);
    });
  }, 5000);
  intervalIds.value.push(id);
};
const activities = ref([]);
const noticeContent = ref(null);
const showScrollHint = ref(false);

// 检查是否需要显示滚动提示
const checkScrollHint = () => {
  if (noticeContent.value) {
    const { scrollHeight, clientHeight } = noticeContent.value;
    showScrollHint.value = scrollHeight > clientHeight;
  }
};

// 判断活动是否可报名
const isActivityAvailable = (activity) => {
  if (!activity) return false;
  
  // 检查活动是否有开始时间和结束时间
  if (!activity.startTime || !activity.stopTime) return false;
  
  const now = new Date();
  const startTime = new Date(activity.startTime);
  const endTime = new Date(activity.stopTime);
  
  // 检查时间是否有效
  if (isNaN(startTime.getTime()) || isNaN(endTime.getTime())) return false;
  
  // 活动在有效时间范围内且未结束
  const isInTimeRange = now >= startTime && now <= endTime;
  
  // 检查是否有名额限制（如果有number字段且大于0）
  const hasAvailableSlots = !activity.number || activity.number > 0;
  
  return isInTimeRange && hasAvailableSlots;
};

// 获取活动状态信息
const getActivityStatus = (activity) => {
  if (!activity) return null;
  
  const now = new Date();
  
  // 检查是否有时间信息
  if (!activity.startTime || !activity.stopTime) {
    return {
      type: 'warning',
      text: '时间未定',
      icon: 'Clock'
    };
  }
  
  const startTime = new Date(activity.startTime);
  const endTime = new Date(activity.stopTime);
  
  // 检查时间是否有效
  if (isNaN(startTime.getTime()) || isNaN(endTime.getTime())) {
    return {
      type: 'warning',
      text: '时间无效',
      icon: 'Warning'
    };
  }
  
  // 活动还未开始
  if (now < startTime) {
    return {
      type: 'info',
      text: '即将开始',
      icon: 'Clock'
    };
  }
  
  // 活动已结束
  if (now > endTime) {
    return {
      type: 'danger',
      text: '已结束',
      icon: 'Close'
    };
  }
  
  // 检查名额
  if (activity.number !== undefined && activity.number <= 0) {
    return {
      type: 'danger',
      text: '已满员',
      icon: 'Close'
    };
  }
  
  return null; // 可报名状态由isActivityAvailable处理
};

findTopStudents()
function getActivities() {
  findActivity().then((res) => {
    activities.value = (res.data.data || []).map((item) => ({ ...item, signed: false }));
    nextTick(() => {
      checkScrollHint();
    });
  });
  const id=setInterval(()=>{
    findActivity().then((res) => {
      activities.value = (res.data.data || []).map((item) => ({ ...item, signed: false }));
      nextTick(() => {
        checkScrollHint();
      });
    });
  }, 5000)
  intervalIds.value.push(id);
}
onUnmounted(() => {
  intervalIds.value.forEach(id => clearInterval(id));
  intervalIds.value = []; // 清空ID数组

  // 清理图表实例
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', resizeChart)
});

onMounted(() => {
  getActivities()
  fetchClassData()
  window.addEventListener('resize', resizeChart)
  // 延迟检查滚动提示，确保DOM已渲染
  setTimeout(() => {
    checkScrollHint();
  }, 1000);
});
</script>

<style scoped>
.home-page {
  padding: 0;
}

/* 欢迎横幅 */
.welcome-banner {
  margin-bottom: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.welcome-banner :deep(.el-card__body) {
  padding: 30px;
}

.banner-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-text h1 {
  margin: 0 0 10px 0;
  font-size: 28px;
  font-weight: 600;
}

.welcome-text p {
  margin: 0;
  opacity: 0.9;
  font-size: 16px;
}

.welcome-stats {
  display: flex;
  gap: 30px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stat-icon {
  font-size: 32px;
  color: #ffd700;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}

/* 主要内容 */
.main-content {
  margin-top: 20px;
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

/* 排行榜卡片 */
.ranking-card {
  margin-bottom: 20px;
}

/* 图表卡片 */
.chart-card {
  margin-bottom: 20px;
}

.chart-content {
  padding: 10px 0;
}

.class-chart {
  width: 100%;
  height: 350px;
}

.ranking-tabs {
  margin-bottom: 20px;
}

.ranking-list {
  max-height: 400px;
  overflow-y: auto;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  transition: all 0.3s;
  cursor: pointer;
}

.ranking-item:hover {
  background: #f5f7fa;
}

.ranking-item.current-student {
  background: #e3f2fd;
  border: 1px solid #2196f3;
}
.toggle-ranking-btn {
  background: #ffffff;
  border: 1px solid #1a4f8c;
  color: #1a4f8c;
  border-radius: 16px;
  padding: 6px 16px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(60, 154, 240, 0.1);
}

.toggle-ranking-btn:hover {
  background: #f0f7ff;
  border-color: #3c9af0;
  color: #3c9af0;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(60, 154, 240, 0.2);
}
.rank-number {
  width: 40px;
  text-align: center;
  margin-right: 12px;
}

.medal-icon {
  font-size: 24px;
}

.medal-1 { color: #ffd700; }
.medal-2 { color: #c0c0c0; }
.medal-3 { color: #cd7f32; }

.rank-text {
  font-size: 18px;
  font-weight: bold;
  color: #666;
}

/* 切换按钮样式 */
.toggle-ranking-btn {
  margin-left: auto;
}

/* 后十名特殊样式 */
.ranking-item.bottom-ranking {
  background: #fef0f0;
  border-left: 3px solid #f56c6c;
}

.ranking-item.bottom-ranking:hover {
  background: #fde2e2;
}

.ranking-item.bottom-ranking .rank-text {
  color: #f56c6c;
}

.ranking-item.bottom-ranking .student-points {
  color: #f56c6c;
}

.student-info {
  flex: 1;
  margin-left: 12px;
}

.student-name {
  font-weight: 600;
  margin-bottom: 4px;
}

.student-class {
  font-size: 12px;
  color: #666;
}

.student-points {
  font-weight: bold;
  color: #2196f3;
}

/* 荣誉墙卡片 */
.honor-categories {
  margin-bottom: 16px;
}

.honor-tag {
  margin-right: 8px;
  cursor: pointer;
}

.honor-list {
  max-height: 300px;
  overflow-y: auto;
}

.honor-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  background: #fafafa;
}

.honor-icon {
  font-size: 20px;
  margin-right: 12px;
  margin-top: 2px;
}

.honor-gold { color: #ffd700; }
.honor-silver { color: #c0c0c0; }
.honor-bronze { color: #cd7f32; }

.honor-title {
  font-weight: 600;
  margin-bottom: 4px;
}

.honor-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.honor-date {
  font-size: 12px;
  color: #999;
}

/* 通知公告卡片 */
.notice-card {
  margin-bottom: 20px;
  height: 600px;
  display: flex;
  flex-direction: column;
}

.notice-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.notice-content {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  padding-right: 8px;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

.notice-content::-webkit-scrollbar {
  width: 6px;
}

.notice-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.notice-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.notice-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.notice-item {
  padding: 16px;
  border-radius: 12px;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.notice-item:hover {
  background: linear-gradient(135deg, #f0f2f5 0%, #e2e8f0 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.notice-item:last-child {
  margin-bottom: 0;
}

/* 可报名提示样式 */
.notice-item {
  position: relative;
}

.signup-badge,
.status-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
  z-index: 10;
  animation: fadeInScale 0.3s ease-out;
  pointer-events: none;
}
.signup-badge .badge-icon,
.signup-badge span,
.status-badge .badge-icon,
.status-badge span {
  pointer-events: none;
}

/* 其余badge样式保持不变... */


@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 状态提示样式 */
.status-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
  animation: fadeInScale 0.3s ease-out;
}

.status-badge.warning {
  background: linear-gradient(135deg, #e6a23c 0%, #f0c78a 100%);
  color: white;
}

.status-badge.info {
  background: linear-gradient(135deg, #909399 0%, #c0c4cc 100%);
  color: white;
}

.status-badge.danger {
  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
  color: white;
}

/* 滚动提示 */
.scroll-hint {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  color: #667eea;
  font-size: 14px;
  font-weight: 500;
  background: linear-gradient(135deg, #f0f2f5 0%, #e2e8f0 100%);
  border-radius: 8px;
  border: 1px dashed #667eea;
  margin-top: 16px;
  animation: pulse 2s infinite;
}

.scroll-hint .el-icon {
  font-size: 16px;
  animation: bounce 1.5s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-4px);
  }
  60% {
    transform: translateY(-2px);
  }
}

.notice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.notice-date {
  font-size: 12px;
  color: #999;
}

.notice-title {
  font-weight: 700;
  margin-bottom: 8px;
  line-height: 1.4;
  color: #2c3e50;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.notice-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
  flex-shrink: 0;
}

.notice-summary {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.6;
  padding-left: 12px;
  border-left: 2px solid #e2e8f0;
  margin-left: 4px;
}

/* 活动详情弹窗样式 */
.activity-detail-dialog {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.activity-detail-dialog .el-dialog {
  border-radius: 16px;
  overflow: hidden;
}

.activity-detail-dialog .el-dialog__header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px 32px;
  margin: 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dialog-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 20px;
  color: #fff;
}

.dialog-title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}

.activity-detail-content {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.activity-form {
  background: white;
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 活动标题 */
.activity-header {
  text-align: center;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 2px solid #f0f2f5;
}

.activity-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-icon {
  font-size: 28px;
  color: #667eea;
}

/* 品字形布局 */
.activity-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto;
  gap: 24px;
  max-width: 100%;
}

.activity-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.activity-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e2e8f0;
}

.section-icon {
  color: #667eea;
  font-size: 18px;
}

.section-title {
  font-weight: 600;
  color: #2c3e50;
  font-size: 16px;
}

.section-content {
  color: #4a5568;
  line-height: 1.6;
  font-size: 14px;
}

.section-content p {
  margin: 0;
  word-break: break-word;
  white-space: pre-wrap;
}

/* 时间信息样式 */
.time-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.time-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 8px;
  border-left: 3px solid #667eea;
}

.time-label {
  font-weight: 500;
  color: #4a5568;
  font-size: 13px;
}

.time-value {
  font-family: 'Monaco', 'Menlo', monospace;
  background: #e2e8f0;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #2c3e50;
  font-weight: 600;
}

/* 参与信息样式 */
.participants-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  text-align: center;
}

.participants-count {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.count-number {
  font-size: 36px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.count-unit {
  font-size: 18px;
  font-weight: 600;
  color: #4a5568;
}

.participants-tag {
  font-weight: 600;
  border-radius: 16px;
  padding: 8px 16px;
  font-size: 14px;
}



.image-container {
  display: flex;
  justify-content: center;
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 2px dashed #667eea;
  transition: all 0.3s ease;
  min-height: 120px;
}

.image-container:hover {
  border-color: #764ba2;
  background: #f8f9fa;
}

.activity-image {
  width: 100%;
  max-width: 200px;
  height: 120px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.8);
}

.activity-image:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.image-error,
.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 200px;
  height: 120px;
  color: #a0aec0;
  background: #f7fafc;
  border-radius: 8px;
  border: 2px dashed #e2e8f0;
}

.image-error .el-icon,
.no-image .el-icon {
  font-size: 24px;
  margin-bottom: 6px;
}

.image-error span,
.no-image span {
  font-size: 12px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 24px 32px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-top: 1px solid #e2e8f0;
}

.dialog-footer .el-button {
  min-width: 120px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  padding: 12px 24px;
  transition: all 0.3s ease;
}

.dialog-footer .el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.3);
}

.dialog-footer .el-button--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.dialog-footer .el-button:not(.el-button--primary) {
  background: white;
  border: 2px solid #e2e8f0;
  color: #4a5568;
}

.dialog-footer .el-button:not(.el-button--primary):hover {
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .activity-detail-dialog {
    width: 95% !important;
    margin: 0 auto;
  }

  .activity-detail-content {
    padding: 16px;
  }

  .activity-form {
    padding: 20px;
  }

  .activity-title {
    font-size: 20px;
  }

  .title-icon {
    font-size: 24px;
  }

  .activity-layout {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .activity-section {
    padding: 16px;
  }

  .count-number {
    font-size: 28px;
  }

  .count-unit {
    font-size: 16px;
  }

  .activity-image {
    max-width: 150px;
    height: 100px;
  }

  .image-error,
  .no-image {
    max-width: 150px;
    height: 100px;
  }

  /* 通知公告响应式 */
  .notice-card {
    height: 500px;
  }

  .notice-item {
    padding: 12px;
    margin-bottom: 12px;
  }

  .notice-title {
    font-size: 14px;
  }

  .notice-summary {
    font-size: 13px;
  }
  
  /* 状态提示响应式 */
  .signup-badge,
  .status-badge {
    font-size: 10px;
    padding: 3px 6px;
  }
  
  .badge-icon {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .notice-card {
    height: 400px;
  }
}

/* 快捷操作卡片 */
.quick-actions {
  display: grid;
  gap: 12px;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s;
}

.action-item:hover {
  background: #f0f0f0;
  transform: translateY(-2px);
}

.action-icon {
  font-size: 24px;
  margin-right: 12px;
}

.action-primary { color: #409eff; }
.action-success { color: #67c23a; }
.action-warning { color: #e6a23c; }
.action-info { color: #909399; }

.action-title {
  font-weight: 600;
  margin-bottom: 4px;
}

.action-desc {
  font-size: 12px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .banner-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .welcome-stats {
    justify-content: center;
  }
}
</style>
