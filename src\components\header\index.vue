<template>
  <div class="header-container">
    <div class="header-left">
      <el-button type="text" @click="toggleSidebar">
        <el-icon size="20px"><Fold v-if="!isSidebarCollapsed" /><Expand v-else /></el-icon>
      </el-button>
      <div class="breadcrumb">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/dashboard' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item v-if="currentRoute.meta.title">{{ currentRoute.meta.title }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </div>
    
    <div class="header-right">
      <el-tooltip content="消息通知" placement="bottom">
        <el-badge :value="unreadCount" class="header-icon" v-if="unreadCount > 0">
          <el-icon size="20px" @click="openNotificationDialog"><Bell /></el-icon>
        </el-badge>
        <el-icon size="20px" class="header-icon" v-else @click="openNotificationDialog"><Bell /></el-icon>
      </el-tooltip>

      <el-dialog v-model="showNotificationDialog" title="未处理申请通知" width="600px" @open="onDialogOpen">
        <div class="dialog-header">
          <span>共 {{ pendingApplications.length }} 条未处理申请</span>
          <el-button type="primary" size="small" @click="loadPendingApplications" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
        <div v-if="pendingApplications.length === 0" class="empty-state">
          <el-empty description="暂无未处理的申请" :image-size="100">
            <el-button type="primary" @click="loadPendingApplications">刷新</el-button>
          </el-empty>
        </div>
        <el-timeline v-else>
          <el-timeline-item 
            v-for="application in pendingApplications" 
            :key="application.applyId" 
            :timestamp="formatTime(application.createTime)" 
            :color="getStatusColor(application)"
          >
            <div class="application-item">
              <div class="application-header">
                                 <span class="student-info">
                   <strong>{{ application.student?.realName || application.studentNo }}</strong>
                   <span class="class-info" v-if="application.student?.className">({{ application.student.className }})</span>
                 </span>
                <span class="points-change" :class="application.pointsChange > 0 ? 'positive' : 'negative'">
                  {{ application.pointsChange > 0 ? '+' : '' }}{{ application.pointsChange }}分
                </span>
              </div>
              <div class="application-reason">
                <strong>申请理由：</strong>{{ application.reason }}
              </div>
              <div class="application-status">
                <el-tag :type="getStatusType(application)" size="small">
                  {{ getStatusText(application) }}
                </el-tag>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
        <template #footer>
          <el-button type="primary" @click="showNotificationDialog = false">关闭</el-button>
        </template>
      </el-dialog>
      
      <el-dropdown trigger="click" @command="handleCommand">
        <div class="user-profile">
          <el-avatar :size="32" :src="avatar" />
          <span class="user-name">{{ username }}</span>
          <el-icon><CaretBottom /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">个人信息</el-dropdown-item>
            <el-dropdown-item command="password">修改密码</el-dropdown-item>
            <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup>
import {ref, computed, inject, onMounted, onUnmounted, watch} from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  Fold, 
  Expand, 
  Bell, 
  CaretBottom,
  Refresh
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { logout } from '@/api/system/user.js'
import { getCurrentUser, clearUserData } from '@/utils/userUtils.js'
import {useCounterStore} from "@/stores/counter.js";
import {storeToRefs} from "pinia";
import { useAppStore } from '@/stores/appStore.js'
import { getPendingApplications } from '@/api/system/points.js'

const route = useRoute()
const router = useRouter()
const isSidebarCollapsed = inject('isSidebarCollapsed', ref(false))
const username =ref(localStorage.getItem("username"));
const avatar =ref(localStorage.getItem("avatar"));
// 监听头像更新事件
const updateAvatar = () => {
  avatar.value = localStorage.getItem("avatar")
}

onUnmounted(() => {
  window.removeEventListener('avatar-updated', updateAvatar)
})
const toggleSidebar = () => {
  isSidebarCollapsed.value = !isSidebarCollapsed.value
}

// User name from stored user data

// Get current user information on component mount
onMounted(() => {
 username.value
  avatar.value
  window.addEventListener('avatar-updated', updateAvatar)
  // 加载未处理申请数据
  loadPendingApplications()
  
  // 设置定时刷新，每5分钟刷新一次
  const refreshInterval = setInterval(loadPendingApplications, 5 * 60 * 1000)
  
  // 组件卸载时清除定时器
  onUnmounted(() => {
    clearInterval(refreshInterval)
  })
})

const currentRoute = computed(() => {
  return route
})

const unreadCount = ref(0) // 未读申请数量
const showNotificationDialog = ref(false)
const pendingApplications = ref([]) // 未处理的申请列表
const loading = ref(false) // 加载状态

// 加载未处理申请数据
const loadPendingApplications = async () => {
  loading.value = true
  try {
    const response = await getPendingApplications()
    if (response.data.code === 200) {
      pendingApplications.value = response.data.data || []
      unreadCount.value = pendingApplications.value.length
    } else {
      console.error('获取未处理申请失败:', response.message)
      ElMessage.error('获取未处理申请失败: ' + response.message)
    }
  } catch (error) {
    console.error('获取未处理申请出错:', error)
    ElMessage.error('获取未处理申请出错，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取状态颜色
const getStatusColor = (application) => {
  // 根据申请状态返回不同颜色
  if (application.status === 1 || application.status1 === 1 || application.status2 === 1) {
    return 'orange' // 待审核
  }
  return 'blue'
}

// 获取状态类型
const getStatusType = (application) => {
  if (application.status === 1 || application.status1 === 1 || application.status2 === 1) {
    return 'warning' // 待审核
  }
  return 'info'
}

// 获取状态文本
const getStatusText = (application) => {
  // 根据用户角色和申请状态返回相应的状态文本
  const roleIds = getRoleIds()
  
  if (roleIds.includes(1)) {
    // 超级管理员
    if (application.status2 === 1) return '院长待审核'
    if (application.status2 === 2) return '院长已通过'
    if (application.status2 === 3) return '院长已拒绝'
  } else if (roleIds.includes(2) || roleIds.includes(3)) {
    // 主任
    if (application.status1 === 1) return '主任待审核'
    if (application.status1 === 2) return '主任已通过'
    if (application.status1 === 3) return '主任已拒绝'
  } else if (roleIds.includes(4) || roleIds.includes(5)) {
    // 讲师/导员
    if (application.status === 1) return '导员/讲师待审核'
    if (application.status === 2) return '导员/讲师已通过'
    if (application.status === 3) return '导员/讲师已拒绝'
  }
  
  return '待审核'
}

// 获取用户角色ID数组
const getRoleIds = () => {
  const roleIdStr = localStorage.getItem('roleId')
  if (!roleIdStr) return []

  try {
    if (roleIdStr.startsWith('[') && roleIdStr.endsWith(']')) {
      return JSON.parse(roleIdStr)
    }
    if (roleIdStr.includes(',')) {
      return roleIdStr.split(',').map(id => parseInt(id.trim()))
    }
    return [parseInt(roleIdStr)]
  } catch (e) {
    console.error('解析roleId失败:', e)
    return []
  }
}

function openNotificationDialog() {
  showNotificationDialog.value = true
}

function onDialogOpen() {
  // 打开对话框时重新加载数据
  loadPendingApplications()
}

const handleCommand = (command) => {
  if (command === 'logout') {
    ElMessageBox.confirm(
      '确定要退出登录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    .then(async () => {
      try {
        // 调用登出API
        await logout();
        // 清除登录信息
        clearUserData();
        localStorage.clear()
        sessionStorage.clear()
        // 跳转到登录页
        window.location.href = '/login';
        ElMessage({
          type: 'success',
          message: '已成功退出登录',
        });
      } catch (error) {
        console.error('登出失败:', error);
        
        // 即使API调用失败，也强制清除本地登录状态
        clearUserData();
        router.push('/');
        
        ElMessage({
          type: 'warning',
          message: '登出过程中出现问题，已强制退出登录',
        });
      }
    })
    .catch(() => {});
  } else if (command === 'profile') {
    router.push('/dashboard/settings/profile');
  } else if (command === 'password') {
    router.push('/dashboard/settings/password');
  }
}

const appStore = useAppStore()

</script>

<style scoped>
.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #fff;
}

.header-left {
  display: flex;
  align-items: center;
}

.breadcrumb {
  margin-left: 20px;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-icon {
  margin-right: 20px;
  cursor: pointer;
}

.user-profile {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.user-name {
  margin: 0 8px;
  font-size: 14px;
}

/* 申请项样式 */
.application-item {
  padding: 8px 0;
}

.application-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.student-info {
  font-size: 14px;
}

.class-info {
  color: #666;
  font-size: 12px;
  margin-left: 4px;
}

.points-change {
  font-weight: bold;
  font-size: 14px;
}

.points-change.positive {
  color: #67c23a;
}

.points-change.negative {
  color: #f56c6c;
}

.application-reason {
  margin-bottom: 8px;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.application-status {
  margin-top: 4px;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}

</style>