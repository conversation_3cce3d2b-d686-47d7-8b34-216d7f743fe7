server:
  port: 8080

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************************************************************************************************
    username: feidaowudi
    password: feidaowudi_520
    # HikariCP连接池配置
    hikari:
      # 连接池最大连接数
      maximum-pool-size: 20
      # 连接池最小空闲连接数
      minimum-idle: 5
      # 连接超时时间(毫秒)
      connection-timeout: 30000
      # 空闲连接存活最大时间(毫秒)
      idle-timeout: 600000
      # 连接最大存活时间(毫秒)
      max-lifetime: 1800000
      # 连接测试查询
      connection-test-query: SELECT 1
  web:
    resources:
      static-locations: file:///D:/javaGallery/imgs
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
mybatis-plus:
  type-aliases-package: com.zhentao.pojo
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 使用简单执行器，避免批量模式影响其他操作
    default-executor-type: simple
    # JDBC批量大小
    default-statement-timeout: 60
  mapper-locations: classpath:mapper/*.xml
  global-config:
    db-config:
      # 批量插入大小
      batch-size: 1000
minio:
  endpoint: http://***************:9000/
  accessKey: minioadmin
  secretKey: minioadmin
  bucket: jifen