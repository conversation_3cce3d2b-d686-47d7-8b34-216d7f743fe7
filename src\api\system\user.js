import request from '@/request';


/**
 * 登录
 * @param {string} username 用户名
 * @param {string} password 密码
 * @returns {Promise}
 */
export function login(username, password) {
  return request({
        url: '/user/login',
        method: 'post',
        params: {
            username,
            password
        }
  });
}

/**
 * 用户注册
 * @param {Object} data 注册信息
 * @returns {Promise}
 */
export function register(data) {
  return request({
    url: '/user/register',
    method: 'post',
    data
  });
}

/**
 * 退出登录
 * @returns {Promise}
 */
export function logout() {
  return request({
        url: '/user/logout',
        method: 'post'
  });
}

/**
 * 获取用户列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getUserList(params) {
  return request({
    url: '/sysUser/getUserPage',
    method: 'post',
    data: params
  });
}

/**
 * 根据类型获取用户列表（讲师、导员、阶段主任等）
 * @param {Number} userType 用户类型（1-讲师，2-导员，3-阶段主任，4-院长，9-超级管理员）
 * @returns {Promise}
 */
export function getUsersByType(userType) {
  return request({
    url: '/sysUser/getUsersByType',
    method: 'get',
    params: { userType }
  });
}



/**
 * 重置用户密码
 * @param {Number} userId 用户ID
 * @returns {Promise}
 */
export function resetUserPassword(userId) {
  return request({
    url: '/sysUser/resetUserPassword',
    method: 'post',
    params: { userId }
  });
}

/**
 * 更新用户状态
 * @param {Number} userId 用户ID
 * @param {Number} status 状态（0-正常，1-禁用）
 * @returns {Promise}
 */
export function updateUserStatus(userId, status) {
  return request({
    url: '/sysUser/updateUserStatus',
    method: 'post',
    params: { userId, status }
  });
}


/**
 * 获取用户分页信息
 * @returns {*}
 */
export function getUserPage(data){
    return request({
        url: '/sysUser/getUserPage',
        method: 'post',
        data
    })
}
/**
 * 添加用户与角色
 * @returns {*}
 */
export function addUserAndRole(data){
    return request({
        url: '/sysUser/addUserRole',
        method: 'post',
        data
    })
}
/**
 * 修改用户与角色信息
 * @returns {*}
 */
export function updateUserRole(data){
    return request({
        url: '/sysUser/updateUserRole',
        method: 'post',
        data
    })
}
/**
 * 删除用户
 * @returns {*}
 */
export function deleteUserById(userId){
    return request({
        url: '/sysUser/deleteUser',
        method: 'post',
        params:{
            userId: userId
        }
    })
}

/**
 * 修改用户个人信息
 * @param {Object} data 用户信息
 * @returns {Promise}
 */
export function updateUserInfo(data){
    return request({
        url: '/sysUser/updateUserInfo',
        method: 'post',
        data
    })
}

/**
 * 修改用户密码
 * @param {String} oldPassword 旧密码
 * @param {String} newPassword 新密码
 * @returns {Promise}
 */
export function updateUserPassword(data){
    return request({
        url: '/sysUser/updateUserPassword',
        method: 'post',
        data
    })
}

/**
 * 获取用户详情
 * @param {Number} userId 用户ID
 * @returns {Promise}
 */
export function getUserDetail(){
    return request({
        url: '/sysUser/getUserDetail',
        method: 'get',
    })
}

export function getStudentJSList0(){
  return request({
    url: '/sysUser/JSList0',
    method: 'post',
  })
}
