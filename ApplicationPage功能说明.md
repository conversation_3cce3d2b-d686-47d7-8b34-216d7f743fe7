# ApplicationPage.vue 学生申请功能实现说明

## 功能概述
为ApplicationPage.vue实现了完整的学生积分申请功能，包括学生搜索、信息自动填充、图片上传等功能。

## 主要功能

### 1. 学生搜索功能
- **搜索方式**: 通过学号或姓名进行远程搜索
- **权限控制**: 根据用户角色控制搜索范围
  - 秘书(role_id=6): 可以搜索所有学生
  - 讲师/导员(role_id=4,5): 只能搜索自己班级的学生
  - 管理员/主任: 可以搜索所有学生
- **API接口**: `/edu-student/search`

### 2. 学生信息自动填充
- 选择学生后自动填充以下信息：
  - 学号 (studentNo)
  - 姓名 (realName) 
  - 班级 (className)
  - 班级ID (classId)

### 3. 申请表单
- **申请类型**: 加分申请(1) / 扣分申请(2)
- **申请分值**: 数字输入，加分最大100分，扣分最大50分
- **申请原因**: 必填，最多500字符
- **证明材料**: 支持图片和PDF文件上传

### 4. 文件上传
- **支持格式**: jpg, jpeg, png, pdf
- **文件大小**: 最大2MB
- **上传地址**: `/minio/upload`
- **存储方式**: 多个文件URL用逗号分隔存储在img字段

### 5. 数据提交
- **API接口**: `/points-apply/studentApply`
- **提交数据格式**:
```json
{
  "studentNo": "学号",
  "classId": 班级ID,
  "pointsChange": 1, // 1-加分，2-扣分
  "points": 分值,
  "reason": "申请原因",
  "img": "图片URL1,图片URL2"
}
```

## 技术实现

### 前端组件结构
```vue
<template>
  <!-- 学生搜索选择器 -->
  <el-select 
    v-model="applicationForm.selectedStudent"
    filterable
    remote
    :remote-method="searchStudentsMethod"
  />
  
  <!-- 学生信息显示 -->
  <div v-if="selectedStudentInfo">
    <!-- 只读显示学号、姓名、班级 -->
  </div>
  
  <!-- 申请表单 -->
  <el-form :model="applicationForm" :rules="applicationRules">
    <!-- 申请类型、分值、原因、文件上传 -->
  </el-form>
</template>
```

### 关键方法
1. **searchStudentsMethod**: 学生搜索方法
2. **handleStudentSelect**: 学生选择处理
3. **submitApplication**: 申请提交
4. **handleUploadSuccess**: 文件上传成功处理
5. **resetForm**: 表单重置

### 状态管理
```javascript
const applicationForm = reactive({
  pointsChange: 1,
  points: null,
  selectedStudent: null,
  studentNo: '',
  classId: null,
  reason: '',
  attachments: [],
  img: ''
})

const selectedStudentInfo = ref(null)
const studentOptions = ref([])
```

## 后端接口

### 学生搜索接口
- **URL**: `GET /edu-student/search`
- **参数**: `keyword` (学号或姓名)
- **返回**: 学生列表，包含学号、姓名、班级等信息

### 申请提交接口  
- **URL**: `POST /points-apply/studentApply`
- **功能**: 创建积分申请记录
- **权限**: 自动设置当前登录用户为申请人
- **审核流程**: 自动设置三级审核状态

## 使用说明

1. **选择学生**: 在搜索框中输入学号或姓名，从下拉列表中选择学生
2. **填写申请**: 选择申请类型、输入分值和原因
3. **上传证明**: 上传相关证明材料（可选）
4. **提交申请**: 点击提交按钮，系统将创建申请记录
5. **查看记录**: 在下方列表中查看申请历史和状态

## 注意事项

1. 必须先选择学生才能提交申请
2. 申请分值有上限限制（加分100分，扣分50分）
3. 上传文件大小不能超过2MB
4. 申请提交后需要经过三级审核流程
5. 秘书角色可以为任何学生申请，其他角色有权限限制
