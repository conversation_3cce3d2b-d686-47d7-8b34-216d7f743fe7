<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus, Edit, Delete, Refresh } from '@element-plus/icons-vue'
import {
  addUserAndRole,
  deleteUserById,
  getUserPage,
  resetUserPassword,
  updateUserRole, updateUserStatus
} from "@/api/system/user.js";
import {findAllRole} from "@/api/system/role.js";

// 搜索表单
const tableData = ref([])
const searchForm = ref({
  pageCurrent:1,
  pageSize:10,
  username: '',
  realName: '',
  roleId: '',
  status: ''
})
const total = ref(0)
const handleSizeChange = (val) => {
  searchForm.value.pageSize = val
  searchForm.value.pageCurrent=1
  fetchData()
}

const handleCurrentChange = (val) => {
  searchForm.value.pageCurrent = val
  fetchData()
}
// 生命周期钩子
onMounted(() => {
  fetchData()
})

// 方法定义
const fetchData = () => {
  loading.value = true
  setTimeout(() => {
    getUserPage(searchForm.value).then(res=>{
      tableData.value=res.data.records
      total.value=res.data.total
    })
    loading.value = false
  }, 500)
}


const resetSearch = () => {
  searchForm.value.username = ''
  searchForm.value.realName = ''
  searchForm.value.roleId = ''
  searchForm.value.status = ''
  searchForm.value.pageCurrent = 1
  fetchData()
}
//-----------------------添加---------------------------
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }

  form.value.id = ''
  form.value.username = ''
  form.value.password = ''
  form.value.realName = ''
  form.value.email = ''
  form.value.phone = ''
  form.value.roleIds = []
  form.value.status = ''
  form.value.remark = ''
}
const formRef = ref(null)
const handleAdd = () => {
  dialogType.value = 'add'
  formTitle.value = '添加用户'
  resetForm()
  dialogVisible.value = true
}
// 角色选项/查询所有角色
const roleOptions = ref([])
const findRoleList=()=>{
  findAllRole().then(res=>{
    roleOptions.value = res.data.data
  })
}
findRoleList()


// 表格数据
const loading = ref(false)

// 对话框控制
const dialogVisible = ref(false)
const dialogType = ref('add') // 'add' 或 'edit'
const formTitle = ref('添加用户')

// 表单数据
const form = ref({
  id: '',
  username: '',
  password: '',
  realName: '',
  email: '',
  phone: '',
  roleIds: [],
  status: '',
  remark: ''
})

// 表单规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  roleIds: [
    { required: true, message: '请选择用户类型', trigger: 'change' }
  ]
}

const handleEdit = (row) => {
  dialogType.value = 'edit'
  formTitle.value = '编辑用户'
  resetForm()
  form.value.userId = row.userId
  form.value.username = row.username
  form.value.realName = row.realName
  form.value.email = row.email
  form.value.phone = row.phone
  form.value.roleIds = row.roleIds
  form.value.status = row.status
  form.value.remark = row.remark
  form.value.password = row.password
  dialogVisible.value = true
}


const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除用户 "${row.username}" 吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
  .then(() => {
    // 删除操作
    deleteUserById(row.userId).then(res => {
      if(res.data.code === 200){
        ElMessage.success(res.data.message)
        fetchData()
      }else {
        ElMessage.error(res.data.message)
      }
    })

  })
  .catch(() => {})
}

const resetPassword = (row) => {
  ElMessageBox.confirm(
    `确定要重置用户 "${row.username}" 的密码吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
  .then(()=>{
    resetUserPassword(row.userId).then((res) => {
      if (res.data.code === 200){
        ElMessage.success(`用户 ${row.username} 的密码已重置为初始密码`)
      }else {
        ElMessage.error(res.data.message)
      }
    })
  })

  .catch(() => {})
}

const changeStatus1 = (row) => {
  ElMessageBox.confirm(
    `确定要该用户 "${row.username}" 改变状态吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
  .then(() => {
    // 模拟状态变更
    updateUserStatus(row.userId, 1).then(res=>{
      if(res.data.code==200){
        ElMessage.success(res.data.message)
        fetchData()
      }else{
        ElMessage.error(res.data.message)
      }
    })
  })
  .catch(() => {})
}
const changeStatus2 = (row) => {
  ElMessageBox.confirm(
      `确定要该用户 "${row.username}" 改变状态吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
  )
      .then(() => {
        // 模拟状态变更
        updateUserStatus(row.userId, 0).then(res=>{
          if(res.data.code==200){
            ElMessage.success(res.data.message)
            fetchData()
          }else{
            ElMessage.error(res.data.message)
          }
        })
      })
      .catch(() => {})
}

const submitForm = () => {
  if (!formRef.value) return
  
  formRef.value.validate((valid) => {
    if (valid) {
      if (dialogType.value === 'add') {
        // 添加用户
        addUserAndRole(form.value).then(res=>{
          if(res.data.code==200){
            ElMessage.success(res.data.message)
            fetchData()
            dialogVisible.value = false
          }else{
            ElMessage.error(res.data.message)
          }
        })
      } else {
        // 编辑用户
       updateUserRole(form.value).then(res=>{
         if(res.data.code==200){
          ElMessage.success(res.data.message)
           dialogVisible.value = false
           fetchData()
        }else{
          ElMessage.error(res.data.message)
        }
       })
      }

    } else {
      return false
    }
  })
}



const handleRefresh = () => {
  fetchData()
}

const getStatusType = (status) => {
  switch (status) {
    case 0: return 'success'
    case 1: return 'danger'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 0: return '正常'
    case 1: return '禁用'
  }
}
const statusOptions = ref([
  { value: 0, label: '正常' },
  { value: 1, label: '禁用' }
])
</script>

<template>
  <div class="user-manage-container">
    <div class="page-header">
      <h2>用户管理</h2>
      <el-button type="primary" :icon="Refresh" circle @click="handleRefresh" />
    </div>

    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户名">
          <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable />
        </el-form-item>
        
        <el-form-item label="姓名">
          <el-input v-model="searchForm.realName" placeholder="请输入姓名" clearable />
        </el-form-item>
        
        <el-form-item label="角色">
          <el-select v-model="searchForm.roleId" placeholder="请选择角色" clearable style="width: 180px;">
            <el-option
              v-for="item in roleOptions"
              :key="item.roleId"
              :label="item.roleName"
              :value="item.roleId"
            />
          </el-select>
        </el-form-item>


        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 180px;">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="fetchData()">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="success" :icon="Plus" @click="handleAdd">添加用户</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
      >
        <el-table-column prop="username" label="用户名" width="120" />
        <el-table-column prop="realName" label="姓名" width="120" />
        <el-table-column prop="email" label="邮箱" width="200" />
        <el-table-column prop="phone" label="手机号" width="140" />
        <el-table-column prop="roleNames" label="用户身份" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="loginDate" label="最后登录时间" width="180" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" fixed="right" width="240">
          <template #default="scope">
            <el-button type="primary" link :icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" link :icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
            <el-button type="warning" link @click="resetPassword(scope.row)">重置密码</el-button>
            <el-button link @click="changeStatus1(scope.row)" type="danger" v-if="scope.row.status === 0">
              禁用
            </el-button>
            <el-button link @click="changeStatus2(scope.row)" type="success" v-else>
              启用
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:currentPage="searchForm.pageCurrent"
          v-model:page-size="searchForm.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    
    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="formTitle"
      width="600px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="form.username"  />
        </el-form-item>

        
        <el-form-item label="姓名" prop="realName" v-if="dialogType === 'add'">
          <el-input v-model="form.realName" />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" />
        </el-form-item>
        
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" />
        </el-form-item>

        <el-form-item label="用户身份" prop="roleIds">
          <el-select
              v-model="form.roleIds"
              multiple
              placeholder="请选择用户类型"
              style="width: 500px"
          >
            <el-option
                v-for="item in roleOptions"
                :key="item.roleId"
                :label="item.roleName"
                :value="item.roleId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="状态" prop="status" v-if="dialogType === 'edit'">
          <el-select v-model="form.status" placeholder="请选择状态">
            <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>

        
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" rows="3" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.user-manage-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  display: flex;
  flex-direction: column;
  min-height: 100%;
}

/* 隐藏WebKit浏览器的滚动条 */
.user-manage-container::-webkit-scrollbar {
  display: none;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 22px;
  color: #303133;
}

.search-container, .table-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  overflow: visible;
  transition: all 0.3s;
  animation: fadeIn 0.5s ease-in-out;
}

.search-container:hover, .table-container:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.pagination-container {
  margin-top: 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 添加动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
}
</style>