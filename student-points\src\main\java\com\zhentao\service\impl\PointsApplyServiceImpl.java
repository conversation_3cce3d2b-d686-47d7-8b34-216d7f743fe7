package com.zhentao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.HashMap;
import java.util.ArrayList;
import com.zhentao.dto.ImportResultDto;
import com.zhentao.dto.PointsApplyImportDto;
import com.zhentao.mapper.PointsApplyMapper;
import com.zhentao.mapper.SysUserRoleMapper;
import com.zhentao.pojo.EduClass;
import com.zhentao.pojo.EduStudent;
import com.zhentao.pojo.PointsApply;
import com.zhentao.pojo.SysUser;
import com.zhentao.pojo.SysUserRole;
import com.zhentao.service.EduClassService;
import com.zhentao.service.EduStudentService;
import com.zhentao.service.PointsApplyService;
import com.zhentao.utils.Result;
import com.zhentao.utils.UserContext;
import com.zhentao.config.MinioConfig;
import io.minio.MinioClient;
import io.minio.ObjectWriteResponse;
import io.minio.PutObjectArgs;
import io.minio.BucketExistsArgs;
import org.apache.poi.hssf.usermodel.HSSFClientAnchor;
import org.apache.poi.hssf.usermodel.HSSFPicture;
import org.apache.poi.hssf.usermodel.HSSFPictureData;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFPictureData;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import jakarta.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 积分变动申请表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Service
public class PointsApplyServiceImpl extends ServiceImpl<PointsApplyMapper, PointsApply> implements PointsApplyService {

    @Autowired
    private PointsApplyMapper pointsApplyMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private EduStudentService eduStudentService;

    @Autowired
    private EduClassService eduClassService;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Autowired
    private MinioConfig minioConfig;

    @Autowired
    private MinioClient minioClient;

    // MinIO配置，可以考虑移到配置文件中
    private static final String MINIO_ENDPOINT = "http://182.254.244.209:9000";
    private static final String MINIO_ACCESS_KEY = "minioadmin";
    private static final String MINIO_SECRET_KEY = "minioadmin";
    private static final String MINIO_BUCKET = "jifen";

    @Override
    public Result addJiFen(@RequestBody PointsApply pointsApply){
        try {
            // 打印接收到的对象
            System.out.println("接收到的积分申请数据: " + pointsApply);
            System.out.println("积分变动值: " + pointsApply.getPointsChange());
            System.out.println("接收到的积分申请对象: " + pointsApply);

            // 创建minio的客户端
            MinioClient minioClient = MinioClient.builder()
                    .credentials(MINIO_ACCESS_KEY, MINIO_SECRET_KEY)
                    .endpoint(MINIO_ENDPOINT)
                    .build();

            // 处理图片路径
            String imgData = pointsApply.getImg();
            String evidenceUrls = "";

            if (imgData != null && !imgData.isEmpty()) {
                // 检查是否有多个图片（以逗号分隔）
                String[] imgItems = imgData.split(",");
                StringBuilder urlBuilder = new StringBuilder();

                for (String imgItem : imgItems) {
                    try {
                        String fileName;
                        InputStream imageStream;

                        // 检查是否是Base64编码的图片数据
                        if (imgItem.startsWith("data:image")) {
                            // 处理Base64编码的图片
                            int commaIndex = imgItem.indexOf(",");
                            if (commaIndex > 0) {
                                String base64Data = imgItem.substring(commaIndex + 1);
                                byte[] imageBytes = Base64.getDecoder().decode(base64Data);
                                imageStream = new ByteArrayInputStream(imageBytes);

                                // 生成唯一文件名
                                String extension = getImageExtension(imgItem);
                                fileName = UUID.randomUUID().toString() + extension;

                                System.out.println("处理Base64图片数据");
                            } else {
                                System.out.println("无效的Base64图片数据");
                                continue;
                            }
                        } else if (imgItem.startsWith("http://") || imgItem.startsWith("https://")) {
                            // 如果已经是URL，直接添加到结果中
                            if (urlBuilder.length() > 0) {
                                urlBuilder.append(",");
                            }
                            urlBuilder.append(imgItem.trim());
                            System.out.println("添加已有URL: " + imgItem.trim());
                            continue;
                        } else {
                            // 尝试作为文件路径处理
                            String filePath = imgItem.trim();
                            File file = new File(filePath);

                            // 检查文件是否存在
                            if (!file.exists() && !file.isAbsolute()) {
                                // 如果不是绝对路径，尝试从D:/img/image/目录读取（兼容旧代码）
                                filePath = "D:/img/image/" + imgItem.trim();
                                file = new File(filePath);
                            }

                            if (file.exists()) {
                                imageStream = new FileInputStream(file);
                                fileName = file.getName();
                                System.out.println("成功读取文件: " + filePath);
                            } else {
                                System.out.println("文件不存在: " + filePath);
                                continue;
                            }
                        }

                        // 执行上传操作
                        PutObjectArgs putObjectArgs = PutObjectArgs.builder()
                                .bucket(MINIO_BUCKET)
                                .object(fileName)
                                .contentType(getContentType(fileName))
                                .stream(imageStream, imageStream.available(), -1)
                                .build();

                        // 执行上传
                        ObjectWriteResponse response = minioClient.putObject(putObjectArgs);
                        System.out.println("File uploaded: " + response);

                        // 获取地址
                        String path = minioClient.getObjectUrl(putObjectArgs.bucket(), putObjectArgs.object());
                        System.out.println("URL: " + path);

                        // 添加到URL列表
                        if (urlBuilder.length() > 0) {
                            urlBuilder.append(",");
                        }
                        urlBuilder.append(path);

                        // 关闭流
                        imageStream.close();
                    } catch (Exception e) {
                        System.out.println("处理图片时出错: " + imgItem);
                        e.printStackTrace();
                    }
                }

                // 设置证据图片URL
                evidenceUrls = urlBuilder.toString();
            }

            // 如果成功上传了图片，设置URL，否则设置为空
            if (!evidenceUrls.isEmpty()) {
                pointsApply.setEvidenceImages(evidenceUrls);
            } else {
                // 如果没有成功上传图片，设置为空字符串，不保存原始数据（可能过长）
                pointsApply.setEvidenceImages("");
                System.out.println("警告: 没有成功上传图片");
            }

            // 设置其他必要字段
            pointsApply.setApplyUserId(UserContext.getCurrentUser().getUserId());
            pointsApply.setCreateTime(new Date());
            // 确保pointsChange字段有值 (1为加分，2为减分)
            if (pointsApply.getPointsChange() == null) {
                System.out.println("警告: pointsChange字段为空，设置默认值");
                pointsApply.setPointsChange(1); // 默认为加分
            }

            // 如果前端没有传递状态，则根据当前用户角色自动设置审核状态
            if (pointsApply.getStatus() == null && pointsApply.getStatus1() == null && pointsApply.getStatus2() == null) {
                setInitialApprovalStatus(pointsApply);
            } else {
                System.out.println("使用前端传递的审核状态 - 导员讲师: " + pointsApply.getStatus() +
                        ", 主任: " + pointsApply.getStatus1() +
                        ", 院长: " + pointsApply.getStatus2());
            }

            // 确保points字段有值
            if (pointsApply.getPoints() == null) {
                System.out.println("警告: points字段为空，设置默认值");
                pointsApply.setPoints(1); // 设置默认值，避免数据库错误
            }

            // 确保classId字段有值
            if (pointsApply.getClassId() == null) {
                System.out.println("警告: classId字段为空，请检查前端传值");
            }

            // 清除原始img字段，避免数据库插入时包含大量Base64数据
            pointsApply.setImg(null);

            System.out.println("插入数据库前的积分申请对象: " + pointsApply);

            // 插入数据库
            int insert = pointsApplyMapper.insert(pointsApply);
            return insert != 0 ? Result.OK() : Result.ERROR();

        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("添加积分申请失败: " + e.getMessage());
        }
    }

    // 根据文件名获取内容类型
    private String getContentType(String fileName) {
        fileName = fileName.toLowerCase();
        if (fileName.endsWith(".jpg") || fileName.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (fileName.endsWith(".png")) {
            return "image/png";
        } else if (fileName.endsWith(".gif")) {
            return "image/gif";
        } else if (fileName.endsWith(".bmp")) {
            return "image/bmp";
        } else if (fileName.endsWith(".webp")) {
            return "image/webp";
        } else {
            return "application/octet-stream";
        }
    }

    // 从Base64数据中获取图片扩展名
    private String getImageExtension(String base64Data) {
        if (base64Data.contains("data:image/jpeg") || base64Data.contains("data:image/jpg")) {
            return ".jpg";
        } else if (base64Data.contains("data:image/png")) {
            return ".png";
        } else if (base64Data.contains("data:image/gif")) {
            return ".gif";
        } else if (base64Data.contains("data:image/bmp")) {
            return ".bmp";
        } else if (base64Data.contains("data:image/webp")) {
            return ".webp";
        } else {
            return ".jpg"; // 默认扩展名
        }
    }

    @Override
    public Result listPointsApplies(Map<String, Object> params) {
        try {
            System.out.println("listPointsApplies 原始参数: " + params);

            // 如果参数为空，创建一个空的Map
            if (params == null) {
                params = new HashMap<>();
            }

            // 如果没有指定pointsChange，默认查询所有记录
            if (!params.containsKey("pointsChange")) {
                System.out.println("未指定pointsChange参数，将查询所有记录");
            }

            System.out.println("listPointsApplies 处理后参数: " + params);

            // 直接使用JDBC查询所有记录，用于调试
            String sql = "SELECT * FROM points_apply LIMIT 10";
            List<Map<String, Object>> debugRecords = jdbcTemplate.queryForList(sql);
            System.out.println("直接JDBC查询到 " + debugRecords.size() + " 条记录");

            // 使用Mapper的自定义方法，直接关联查询学生和班级信息
            List<Map<String, Object>> resultList = pointsApplyMapper.listPointsAppliesWithStudentInfo(params);
            System.out.println("listPointsApplies 结果数量: " + (resultList != null ? resultList.size() : 0));

            // 如果结果为空，返回空数组而不是null
            if (resultList == null) {
                resultList = new ArrayList<>();
            }

            return Result.OK(resultList);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("获取积分申请列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result getPointsHistory(PointsApply pointsApply) {
        // 添加空值检查，确保分页参数有默认值
        if (pointsApply == null) {
            pointsApply = new PointsApply();
        }

        // 设置默认分页参数
        Integer pageNum = pointsApply.getPageNum();
        Integer pageSize = pointsApply.getPageSize();
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }

        // 权限控制逻辑：获取当前用户信息和角色
        SysUser currentUser = UserContext.getCurrentUser();
        Integer currentUserId = null;
        boolean canViewAll = false;

        if (currentUser != null) {
            currentUserId = currentUser.getUserId();

            // 查询用户的角色
            QueryWrapper<SysUserRole> roleQueryWrapper = new QueryWrapper<>();
            roleQueryWrapper.eq("user_id", currentUserId);
            List<SysUserRole> userRoles = sysUserRoleMapper.selectList(roleQueryWrapper);

            List<Integer> roleIds = userRoles.stream()
                    .map(SysUserRole::getRoleId)
                    .collect(Collectors.toList());

            System.out.println("当前用户角色ID列表: " + roleIds);
            System.out.println("查询参数详情:");
            System.out.println("  - studentNo: " + pointsApply.getStudentNo());
            System.out.println("  - pointsChange: " + pointsApply.getPointsChange());
            System.out.println("  - classId: " + pointsApply.getClassId());
            System.out.println("  - status: " + pointsApply.getStatus());
            System.out.println("  - applyUserId: " + pointsApply.getApplyUserId());

            // 权限控制：根据用户角色决定查询范围
            // 角色权限分级：
            // 1=管理员, 2=专业主任, 3=专病主任, 6=秘书：可以查看所有记录
            // 4=讲师, 5=导员：只能查看自己申请的记录
            canViewAll = roleIds.contains(1) || roleIds.contains(2) || roleIds.contains(3) || roleIds.contains(6);

            if (canViewAll) {
                if (roleIds.contains(6)) {
                    System.out.println("用户具有秘书角色，可以查看所有申请（帮扶所有老师和导员）");
                } else {
                    System.out.println("用户具有管理员/主任角色，可以查看所有申请");
                }
            } else {
                System.out.println("用户是讲师/导员，只能查看自己申请的记录");
            }
        }

        Page<PointsApply> page = new Page<>(pageNum, pageSize);
        QueryWrapper<PointsApply> queryWrapper = new QueryWrapper<>();
        // 添加删除标志过滤条件：1-存在，2-删除，数据类型为Integer，所以查询del_flag=1的记录
        queryWrapper.eq("del_flag", 1);

        // 修复查询条件：只有当参数不为null且不为空字符串时才添加查询条件
        // 注意：MyBatis-Plus的eq方法第一个参数是条件判断，只有为true时才会添加该条件

        // 学号条件：只有当学号不为null且不为空时才添加
        if (pointsApply.getStudentNo() != null && !pointsApply.getStudentNo().trim().isEmpty()) {
            queryWrapper.eq("student_no", pointsApply.getStudentNo());
            System.out.println("添加学号查询条件: " + pointsApply.getStudentNo());
        }

        // 积分变动类型条件：只有当不为null时才添加
        if (pointsApply.getPointsChange() != null) {
            queryWrapper.eq("points_change", pointsApply.getPointsChange());
            System.out.println("添加积分变动查询条件: " + pointsApply.getPointsChange());
        }

        // 权限控制：讲师、导员、秘书只能查看自己申请的记录
        if (!canViewAll && currentUserId != null) {
            queryWrapper.eq("apply_user_id", currentUserId);
            System.out.println("添加权限限制条件 apply_user_id: " + currentUserId);
        }

        // 暂时去掉其他可能导致问题的条件
        // queryWrapper.eq(pointsApply.getClassId() != null, "class_id", pointsApply.getClassId());
        // queryWrapper.eq(pointsApply.getStatus() != null, "status", pointsApply.getStatus());
        // queryWrapper.eq(pointsApply.getCreateBy() != null, "create_by", pointsApply.getCreateBy());
        // queryWrapper.eq(pointsApply.getReviewerId() != null, "reviewer_id", pointsApply.getReviewerId());
        // queryWrapper.between(pointsApply.getStartTime() != null && pointsApply.getEndTime() != null,
        //         "create_time", pointsApply.getStartTime(), pointsApply.getEndTime());
        // 添加按创建时间倒序排序
        queryWrapper.orderByDesc("create_time");

        // 添加调试信息
        System.out.println("getPointsHistory 查询条件: " + queryWrapper.getSqlSegment());
        System.out.println("分页参数: pageNum=" + pageNum + ", pageSize=" + pageSize);

        // 查询分页数据
        Page<PointsApply> pageResult = pointsApplyMapper.selectPage(page, queryWrapper);

        System.out.println("getPointsHistory 查询结果: total=" + pageResult.getTotal() + ", records=" + pageResult.getRecords().size());

        // 如果查询成功，填充学生和班级信息
        if (pageResult != null && pageResult.getRecords() != null) {
            for (PointsApply record : pageResult.getRecords()) {
                // 获取学生信息
                if (record.getStudentNo() != null) {
                    QueryWrapper<EduStudent> studentWrapper = new QueryWrapper<>();
                    studentWrapper.eq("student_no", record.getStudentNo());
                    EduStudent student = eduStudentService.getOne(studentWrapper);
                    if (student != null) {
                        record.setStudent(student);
                        // 设置学生姓名和学号
                        record.setStudentName(student.getRealName());
                        record.setStudentNo(student.getStudentNo()); // 添加学号字段
                    }
                }

                // 获取班级信息
                if (record.getClassId() != null) {
                    EduClass eduClass = eduClassService.getById(record.getClassId());
                    if (eduClass != null) {
                        record.setEduClass(eduClass);
                        // 设置班级名称
                        record.setClassName(eduClass.getClassName());
                    }
                }

                // 设置积分类型文本
                if (record.getPointsChange() != null) {
                    record.setType(record.getPointsChange() == 1 ? "add" : "deduct");
                }

                // 设置状态文本
                if (record.getStatus() != null) {
                    switch (record.getStatus()) {
                        case 1:
                            record.setStatusText("pending");
                            break;
                        case 2:
                            record.setStatusText("approved");
                            break;
                        case 3:
                            record.setStatusText("rejected");
                            break;
                        case 4:
                            record.setStatusText("canceled");
                            break;
                        default:
                            record.setStatusText("unknown");
                    }
                }
            }
        }

        return pageResult != null ? Result.OK(pageResult) : Result.ERROR();
    }

    @Override
    public Result getPointsStatistics(Map<String, Object> params) {
        try {
            System.out.println("getPointsStatistics 原始参数: " + params);

            // 处理日期范围参数
            if (params != null && params.containsKey("dateRange")) {
                Object dateRange = params.get("dateRange");
                if (dateRange instanceof List && ((List<?>) dateRange).size() == 2) {
                    List<?> range = (List<?>) dateRange;
                    params.put("startDate", range.get(0));
                    params.put("endDate", range.get(1));
                }
                // 移除原始dateRange参数
                params.remove("dateRange");
            }

            System.out.println("getPointsStatistics 处理后参数: " + params);

            // 调用Mapper查询统计信息
            Map<String, Object> statistics = pointsApplyMapper.getPointsStatistics(params);

            System.out.println("getPointsStatistics 结果: " + statistics);

            // 如果没有数据，返回默认值
            if (statistics == null) {
                statistics = new HashMap<>();
                statistics.put("totalAddPoints", 0);
                statistics.put("totalDeductPoints", 0);
                statistics.put("pendingCount", 0);
                statistics.put("approvedCount", 0);
                statistics.put("rejectedCount", 0);
            }

            // 处理可能为null的值或类型转换
            Object addPoints = statistics.get("totalAddPoints");
            if (addPoints == null) {
                statistics.put("totalAddPoints", 0);
            } else if (addPoints instanceof Number) {
                statistics.put("totalAddPoints", ((Number)addPoints).intValue());
            }

            Object deductPoints = statistics.get("totalDeductPoints");
            if (deductPoints == null) {
                statistics.put("totalDeductPoints", 0);
            } else if (deductPoints instanceof Number) {
                statistics.put("totalDeductPoints", ((Number)deductPoints).intValue());
            }

            // 计算净积分 - 使用Number类型安全地获取值
            int totalAddPoints = statistics.get("totalAddPoints") instanceof Number ?
                    ((Number)statistics.get("totalAddPoints")).intValue() : 0;

            int totalDeductPoints = statistics.get("totalDeductPoints") instanceof Number ?
                    ((Number)statistics.get("totalDeductPoints")).intValue() : 0;

            statistics.put("netPoints", totalAddPoints - totalDeductPoints);

            return Result.OK(statistics);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("获取积分统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 撤销积分申请
     * @param applyId 申请ID
     * @return Result
     */
    @Override
    public Result cancelPointsApplication(Integer applyId) {
        try {
            if (applyId == null) {
                return Result.ERROR("申请ID不能为空");
            }

            // 查询申请记录
            PointsApply pointsApply = pointsApplyMapper.selectById(applyId);
            if (pointsApply == null) {
                return Result.ERROR("申请记录不存在");
            }

            // 检查状态是否为待审核
            if (pointsApply.getStatus() != 1) {
                return Result.ERROR("只有待审核的申请才能撤销");
            }

            // 更新状态为已撤销(0)
            pointsApply.setStatus(0);
            pointsApply.setUpdateTime(new Date());
            pointsApply.setUpdateBy(UserContext.getCurrentUser().getUserId());

            int result = pointsApplyMapper.updateById(pointsApply);

            return result > 0 ? Result.OK("撤销成功") : Result.ERROR("撤销失败");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("撤销申请失败: " + e.getMessage());
        }
    }
    //-----------------------------------------------虚线一下全都是cmy写的--------------------------------------------------------------------
    @Override
    public Result queryTodayAddPoints() {
        QueryWrapper<PointsApply> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("points_change",1);
        queryWrapper.eq("status",2);
        queryWrapper.eq("status1",2);
        queryWrapper.eq("status2",2);
        //查询当天的一个加分记录
        // queryWrapper.ge("create_time", LocalDate.now().atStartOfDay()).lt("create_time", LocalDate.now().plusDays(1).atStartOfDay());
        List<PointsApply> pointsApplies = pointsApplyMapper.selectList(queryWrapper);
        for (PointsApply pointsApply : pointsApplies) {
            String studentNo = pointsApply.getStudentNo();
            QueryWrapper<EduStudent> studentWrapper = new QueryWrapper<>();
            studentWrapper.eq("student_no", studentNo);
            EduStudent student = eduStudentService.getOne(studentWrapper);
            if (student != null) {
                pointsApply.setStudentName(student.getRealName());
            }
            // 确保evidenceImages字段有值
            if (pointsApply.getEvidenceImages() == null) {
                pointsApply.setEvidenceImages("");
            }
        }
        return Result.OK(pointsApplies);
    }

    @Override
    public Result queryTodayMinusPoints() {
        QueryWrapper<PointsApply> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("points_change",2);
        queryWrapper.eq("status",2);
        queryWrapper.eq("status1",2);
        queryWrapper.eq("status2",2);
        //查询当天的一个加分记录
        // queryWrapper.ge("create_time", LocalDate.now().atStartOfDay()).lt("create_time", LocalDate.now().plusDays(1).atStartOfDay());
        List<PointsApply> pointsApplies = pointsApplyMapper.selectList(queryWrapper);
        for (PointsApply pointsApply : pointsApplies) {
            String studentNo = pointsApply.getStudentNo();
            QueryWrapper<EduStudent> studentWrapper = new QueryWrapper<>();
            studentWrapper.eq("student_no", studentNo);
            EduStudent student = eduStudentService.getOne(studentWrapper);
            if (student != null) {
                pointsApply.setStudentName(student.getRealName());
                EduClass byId = eduClassService.getById(student.getClassId());
                pointsApply.setClassName(byId.getClassName());
            }
        }
        return Result.OK(pointsApplies);
    }

    /**
     * 根据当前用户角色自动设置初始审核状态
     * @param pointsApply 积分申请对象
     */
    private void setInitialApprovalStatus(PointsApply pointsApply) {
        try {
            SysUser currentUser = UserContext.getCurrentUser();
            if (currentUser == null) {
                // 如果无法获取当前用户，设置默认状态（全部待审核）
                pointsApply.setStatus(1);    // 导员讲师审核：待审核
                pointsApply.setStatus1(1);   // 主任审核：待审核
                pointsApply.setStatus2(1);   // 院长审核：待审核
                return;
            }

            Integer userId = currentUser.getUserId();

            // 查询用户的角色
            QueryWrapper<SysUserRole> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            List<SysUserRole> userRoles = sysUserRoleMapper.selectList(queryWrapper);

            // 提取角色ID列表
            List<Integer> roleIds = userRoles.stream()
                    .map(SysUserRole::getRoleId)
                    .collect(Collectors.toList());

            System.out.println("当前用户角色ID列表: " + roleIds);

            // 初始化所有状态为待审核
            pointsApply.setStatus(1);    // 导员讲师审核：待审核
            pointsApply.setStatus1(1);   // 主任审核：待审核
            pointsApply.setStatus2(1);   // 院长审核：待审核

            // 根据用户角色自动通过相应的审核
            // 角色映射：1-超级管理员, 2-专业主任, 3-专病主任, 4-讲师, 5-导员, 6-秘书
            // 权限从高到低：1 > 2,3 > 4,5 > 6

            // 按权限从高到低判断，取最高权限角色
            if (roleIds.contains(1)) {
                // 超级管理员(1)拥有最高权限，自动通过所有审核
                pointsApply.setStatus(2);   // 自动通过导员讲师审核
                pointsApply.setStatus1(2);  // 自动通过主任审核
                pointsApply.setStatus2(1);  // 自动通过院长审核
                System.out.println("用户具有超级管理员角色，自动通过所有审核");
            } else if (roleIds.contains(2) || roleIds.contains(3)) {
                // 专业主任(2)或专病主任(3)，自动通过导员讲师审核和主任审核
                pointsApply.setStatus(2);   // 自动通过导员讲师审核
                pointsApply.setStatus1(2);  // 自动通过主任审核
                System.out.println("用户具有主任角色，自动通过导员讲师审核和主任审核");
            } else if (roleIds.contains(4) || roleIds.contains(5)) {
                // 讲师(4)或导员(5)，自动通过导员讲师审核
                pointsApply.setStatus(2);
                System.out.println("用户具有讲师/导员角色，自动通过导员讲师审核");
            } else if (roleIds.contains(6)) {
                // 秘书(6)，所有审核都需要等待
                System.out.println("用户具有秘书角色，所有审核都需要等待");
            }

            System.out.println("最终审核状态 - 导员讲师: " + pointsApply.getStatus() +
                    ", 主任: " + pointsApply.getStatus1() +
                    ", 院长: " + pointsApply.getStatus2());

        } catch (Exception e) {
            System.err.println("设置初始审核状态时出错: " + e.getMessage());
            e.printStackTrace();
            // 出错时设置默认状态
            pointsApply.setStatus(1);
            pointsApply.setStatus1(1);
            pointsApply.setStatus2(1);
        }
    }

    /**
     * 获取讲师/导员的积分历史记录（只能看到自己班级学生的申请和自己创建的申请）
     */
    private Result getPointsHistoryForTeacher(PointsApply pointsApply, Integer teacherId) {
        try {
            // 设置默认分页参数
            Integer pageNum = pointsApply.getPageNum();
            Integer pageSize = pointsApply.getPageSize();
            if (pageNum == null || pageNum < 1) {
                pageNum = 1;
            }
            if (pageSize == null || pageSize < 1) {
                pageSize = 10;
            }

            // 构建查询参数
            Map<String, Object> params = new HashMap<>();
            if (pointsApply.getStudentNo() != null) {
                params.put("studentNo", pointsApply.getStudentNo());
            }
            if (pointsApply.getPointsChange() != null) {
                params.put("pointsChange", pointsApply.getPointsChange());
            }
            if (pointsApply.getStatus() != null) {
                params.put("status", pointsApply.getStatus());
            }
            // 其他查询条件可以根据需要添加

            System.out.println("教师查询参数: " + params + ", 教师ID: " + teacherId);

            // 使用专门的教师查询方法
            List<Map<String, Object>> records = pointsApplyMapper.getPointsHistoryForTeacher(params, teacherId);

            System.out.println("教师查询结果数量: " + (records != null ? records.size() : 0));

            // 手动分页
            int total = records != null ? records.size() : 0;
            int startIndex = (pageNum - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, total);

            List<Map<String, Object>> pagedRecords = new ArrayList<>();
            if (records != null && startIndex < total) {
                pagedRecords = records.subList(startIndex, endIndex);
            }

            // 构建分页结果
            Map<String, Object> result = new HashMap<>();
            result.put("records", pagedRecords);
            result.put("total", total);
            result.put("size", pageSize);
            result.put("current", pageNum);
            result.put("pages", (total + pageSize - 1) / pageSize);

            return Result.OK(result);

        } catch (Exception e) {
            System.err.println("获取教师积分历史时出错: " + e.getMessage());
            e.printStackTrace();
            return Result.ERROR("获取积分历史失败: " + e.getMessage());
        }
    }

    /**
     * 获取秘书的积分历史记录（只能看到自己申请的记录）
     */
    private Result getPointsHistoryForSecretary(PointsApply pointsApply, Integer secretaryId) {
        try {
            // 设置默认分页参数
            Integer pageNum = pointsApply.getPageNum();
            Integer pageSize = pointsApply.getPageSize();
            if (pageNum == null || pageNum < 1) {
                pageNum = 1;
            }
            if (pageSize == null || pageSize < 1) {
                pageSize = 10;
            }

            System.out.println("秘书查询自己申请的记录，用户ID: " + secretaryId);

            // 使用专门的秘书查询方法
            List<Map<String, Object>> records = pointsApplyMapper.getPointsHistoryByApplyUser(secretaryId);

            System.out.println("秘书查询结果数量: " + (records != null ? records.size() : 0));

            // 手动分页
            int total = records != null ? records.size() : 0;
            int startIndex = (pageNum - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, total);

            List<Map<String, Object>> pagedRecords = new ArrayList<>();
            if (records != null && startIndex < total) {
                pagedRecords = records.subList(startIndex, endIndex);
            }

            // 构建分页结果
            Map<String, Object> result = new HashMap<>();
            result.put("records", pagedRecords);
            result.put("total", total);
            result.put("size", pageSize);
            result.put("current", pageNum);
            result.put("pages", (total + pageSize - 1) / pageSize);

            return Result.OK(result);

        } catch (Exception e) {
            System.err.println("获取秘书积分历史时出错: " + e.getMessage());
            e.printStackTrace();
            return Result.ERROR("获取积分历史失败: " + e.getMessage());
        }
    }

    /**
     * 下载积分申请导入模板
     */
    @Override
    public void downloadPointsApplyTemplate(HttpServletResponse response) throws Exception {
        XSSFWorkbook workbook = null;
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("积分申请导入模板", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Cache-Control", "no-cache");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");

            // 创建Excel工作簿
            workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("积分申请导入");

            // 创建表头样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);

            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);

            // 创建表头
            Row headerRow = sheet.createRow(0);
            String[] headers = {"学号", "姓名", "班级", "申请类型", "分值", "申请原因", "证明素材"};

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // 创建字段说明行
            Row descRow = sheet.createRow(1);
            String[] descriptions = {
                "必填：学生学号",
                "必填：学生姓名",
                "必填：班级名称",
                "必填：加分/减分",
                "必填：分值数字",
                "必填：申请原因",
                "选填：证明材料文字说明"
            };
            for (int i = 0; i < descriptions.length; i++) {
                Cell cell = descRow.createCell(i);
                cell.setCellValue(descriptions[i]);
                CellStyle descStyle = workbook.createCellStyle();
                descStyle.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());
                descStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                descStyle.setBorderTop(BorderStyle.THIN);
                descStyle.setBorderRight(BorderStyle.THIN);
                descStyle.setBorderBottom(BorderStyle.THIN);
                descStyle.setBorderLeft(BorderStyle.THIN);
                cell.setCellStyle(descStyle);
            }

            // 创建示例数据行（标注为示例，不会被解析）
            Row exampleRow = sheet.createRow(2);
            String[] examples = {"示例：2023001001", "张三", "计算机2023-1班", "加分", "5", "参加校级比赛获得三等奖", "http://minio:9000/bucket/image.jpg 或 获奖证书说明"};

            for (int i = 0; i < examples.length; i++) {
                Cell cell = exampleRow.createCell(i);
                cell.setCellValue(examples[i]);
                // 为示例行设置特殊样式
                CellStyle exampleStyle = workbook.createCellStyle();
                exampleStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
                exampleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                exampleStyle.setBorderTop(BorderStyle.THIN);
                exampleStyle.setBorderRight(BorderStyle.THIN);
                exampleStyle.setBorderBottom(BorderStyle.THIN);
                exampleStyle.setBorderLeft(BorderStyle.THIN);
                cell.setCellStyle(exampleStyle);
            }

            // 设置列宽
            sheet.setColumnWidth(0, 15 * 256); // 学号
            sheet.setColumnWidth(1, 12 * 256); // 姓名
            sheet.setColumnWidth(2, 20 * 256); // 班级
            sheet.setColumnWidth(3, 10 * 256); // 申请类型
            sheet.setColumnWidth(4, 8 * 256);  // 分值
            sheet.setColumnWidth(5, 30 * 256); // 申请原因
            sheet.setColumnWidth(6, 25 * 256); // 证明素材

            // 写入响应流
            workbook.write(response.getOutputStream());

        } finally {
            if (workbook != null) {
                workbook.close();
            }
        }
    }

    /**
     * 批量导入积分申请（解析Excel文件）
     */
    @Override
    public ImportResultDto batchImportPointsApply(MultipartFile file) throws Exception {
        System.out.println("=== 开始Excel导入处理 ===");
        System.out.println("文件名: " + file.getOriginalFilename());
        System.out.println("文件大小: " + file.getSize() + " bytes");

        long startTime = System.currentTimeMillis();
        ImportResultDto result = new ImportResultDto();

        try (InputStream inputStream = file.getInputStream()) {
            Workbook workbook;

            // 根据文件后缀判断Excel版本
            String fileName = file.getOriginalFilename();
            if (fileName == null) {
                throw new Exception("文件名为空");
            }

            fileName = fileName.toLowerCase();
            if (fileName.endsWith(".xlsx")) {
                workbook = new XSSFWorkbook(inputStream);
            } else if (fileName.endsWith(".xls")) {
                workbook = new HSSFWorkbook(inputStream);
            } else {
                throw new Exception("不支持的文件格式，请上传.xlsx或.xls格式的Excel文件");
            }

            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);

            // 从第三行开始读取数据（第一行是表头，第二行是说明）
            int firstRowNum = sheet.getFirstRowNum() + 2;
            int lastRowNum = sheet.getLastRowNum();

            if (lastRowNum < firstRowNum) {
                result.addErrorSummary("Excel文件中没有数据行");
                return result;
            }

            // 解析每一行数据
            System.out.println("开始遍历Excel行，从第" + (firstRowNum + 1) + "行到第" + (lastRowNum + 1) + "行");
            for (int rowIndex = firstRowNum; rowIndex <= lastRowNum; rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) {
                    System.out.println("第" + (rowIndex + 1) + "行为空，跳过");
                    continue;
                }

                System.out.println("=== 处理第" + (rowIndex + 1) + "行 ===");

                // 检查是否是示例行（学号列以"示例："开头）
                Cell firstCell = row.getCell(0);
                if (firstCell != null) {
                    String firstCellValue = getCellStringValue(firstCell);
                    if (firstCellValue != null && firstCellValue.startsWith("示例：")) {
                        // 跳过示例行
                        continue;
                    }
                }

                PointsApplyImportDto importDto = parseRowData(row, rowIndex + 1);
                if (importDto != null) {
                    result.getDetails().add(importDto);
                }
            }

            // 验证数据
            validateImportData(result);

            // 计算统计信息
            result.calculateStats();
            result.setProcessTime(System.currentTimeMillis() - startTime);

            workbook.close();

        } catch (Exception e) {
            result.addErrorSummary("文件解析失败: " + e.getMessage());
            throw e;
        }

        return result;
    }

    /**
     * 解析Excel行数据
     */
    private PointsApplyImportDto parseRowData(Row row, int rowIndex) {
        PointsApplyImportDto dto = new PointsApplyImportDto();
        dto.setRowIndex(rowIndex);

        try {
            // 学号 (A列)
            Cell studentNoCell = row.getCell(0);
            if (studentNoCell != null) {
                String studentNo = getCellStringValue(studentNoCell);
                // 跳过空行或示例行
                if (studentNo == null || studentNo.trim().isEmpty() || studentNo.startsWith("示例：")) {
                    return null;
                }
                dto.setStudentNo(studentNo);
            } else {
                // 学号为空，跳过这行
                return null;
            }

            // 姓名 (B列)
            Cell studentNameCell = row.getCell(1);
            if (studentNameCell != null) {
                dto.setStudentName(getCellStringValue(studentNameCell));
            }

            // 班级 (C列)
            Cell classNameCell = row.getCell(2);
            if (classNameCell != null) {
                dto.setClassName(getCellStringValue(classNameCell));
            }

            // 申请类型 (D列)
            Cell pointsChangeCell = row.getCell(3);
            if (pointsChangeCell != null) {
                String pointsChangeText = getCellStringValue(pointsChangeCell);
                dto.setPointsChangeText(pointsChangeText);

                if ("加分".equals(pointsChangeText)) {
                    dto.setPointsChange(1);
                } else if ("减分".equals(pointsChangeText)) {
                    dto.setPointsChange(2);
                }
            }

            // 分值 (E列)
            Cell pointsCell = row.getCell(4);
            if (pointsCell != null) {
                try {
                    double pointsValue = pointsCell.getNumericCellValue();
                    dto.setPoints((int) pointsValue);
                } catch (Exception e) {
                    String pointsStr = getCellStringValue(pointsCell);
                    if (pointsStr != null && !pointsStr.trim().isEmpty()) {
                        try {
                            dto.setPoints(Integer.parseInt(pointsStr.trim()));
                        } catch (NumberFormatException nfe) {
                            dto.setIsValid(false);
                            dto.setErrorMessage("分值格式错误");
                        }
                    }
                }
            }

            // 申请原因 (F列)
            Cell reasonCell = row.getCell(5);
            if (reasonCell != null) {
                dto.setReason(getCellStringValue(reasonCell));
            }

            // 证明素材 (G列)
            Cell evidenceCell = row.getCell(6);
            if (evidenceCell != null) {
                String evidenceMaterial = getCellStringValue(evidenceCell);
                System.out.println("第" + (rowIndex + 1) + "行证明材料原始值: [" + evidenceMaterial + "]");

                if (evidenceMaterial != null && !evidenceMaterial.trim().isEmpty()) {
                    String trimmed = evidenceMaterial.trim();
                    System.out.println("第" + (rowIndex + 1) + "行证明材料处理后: [" + trimmed + "]");

                    // 检查是否是DISPIMG函数（支持_xlfn.前缀）
                    if (trimmed.startsWith("=DISPIMG(") || trimmed.startsWith("=_xlfn.DISPIMG(")) {
                        System.out.println("检测到DISPIMG函数: " + trimmed);
                        String imageId = extractImageIdFromDISPIMG(trimmed);
                        if (imageId != null) {
                            System.out.println("成功提取图片ID: " + imageId);
                            // 尝试从Excel中提取图片并上传到MinIO
                            String imageUrl = extractAndUploadImageById(row.getSheet().getWorkbook(), imageId);
                            if (imageUrl != null) {
                                System.out.println("图片上传成功，URL: " + imageUrl);
                                dto.setEvidenceMaterial(imageUrl);
                            } else {
                                System.out.println("图片上传失败");
                                dto.setEvidenceMaterial("图片提取失败: " + imageId);
                            }
                        } else {
                            System.out.println("无法提取图片ID");
                            dto.setEvidenceMaterial("无效的DISPIMG函数");
                        }
                    } else if (!trimmed.startsWith("_xlfn.") && !trimmed.startsWith("=")) {
                        // 普通文本或URL
                        System.out.println("普通文本证明材料: " + trimmed);
                        dto.setEvidenceMaterial(trimmed);
                    } else {
                        System.out.println("跳过的公式或特殊内容: " + trimmed);
                    }
                } else {
                    System.out.println("第" + (rowIndex + 1) + "行证明材料为空");
                }
            }

            // TODO: Excel图片提取功能暂时禁用，等待POI版本兼容性问题解决
            // 目前支持在证明材料列填写MinIO URL或文本说明
            // String imageUrls = extractAndUploadImagesFromRow(row, rowIndex);
            // if (imageUrls != null && !imageUrls.isEmpty()) {
            //     if (dto.getEvidenceMaterial() != null && !dto.getEvidenceMaterial().isEmpty()) {
            //         dto.setEvidenceMaterial(dto.getEvidenceMaterial() + "," + imageUrls);
            //     } else {
            //         dto.setEvidenceMaterial(imageUrls);
            //     }
            // }

        } catch (Exception e) {
            dto.setIsValid(false);
            dto.setErrorMessage("行数据解析失败: " + e.getMessage());
        }

        return dto;
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 处理数字类型，避免科学计数法
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == (long) numericValue) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                // 返回完整的公式，包括等号前缀
                return "=" + cell.getCellFormula();
            default:
                return "";
        }
    }

    private String extractImageIdFromDISPIMG(String dispimgFormula) {
        try {
            // 匹配 =DISPIMG("ID_...",1) 或 =_xlfn.DISPIMG("ID_...",1) 格式
            if ((dispimgFormula.startsWith("=DISPIMG(\"") || dispimgFormula.startsWith("=_xlfn.DISPIMG(\""))
                && dispimgFormula.contains("\",")) {

                int startIndex = dispimgFormula.indexOf("\"") + 1;
                int endIndex = dispimgFormula.indexOf("\",", startIndex);
                if (startIndex > 0 && endIndex > startIndex) {
                    String imageId = dispimgFormula.substring(startIndex, endIndex);
                    System.out.println("提取到图片ID: " + imageId);
                    return imageId;
                }
            }
            System.out.println("无法匹配DISPIMG格式: " + dispimgFormula);
            return null;
        } catch (Exception e) {
            System.err.println("提取DISPIMG图片ID失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 根据图片ID从Excel工作簿中提取图片并上传到MinIO
     */
    private String extractAndUploadImageById(Workbook workbook, String imageId) {
        try {
            System.out.println("开始提取图片ID: " + imageId);

            // 获取工作簿中的所有图片
            if (workbook instanceof XSSFWorkbook) {
                return extractImageFromXSSFWorkbook((XSSFWorkbook) workbook, imageId);
            } else if (workbook instanceof HSSFWorkbook) {
                return extractImageFromHSSFWorkbook((HSSFWorkbook) workbook, imageId);
            }

            System.out.println("不支持的工作簿类型");
            return null;

        } catch (Exception e) {
            System.err.println("根据ID提取图片失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 从XSSF工作簿中根据ID提取图片
     */
    private String extractImageFromXSSFWorkbook(XSSFWorkbook workbook, String imageId) {
        try {
            System.out.println("开始从XSSF工作簿提取图片，目标ID: " + imageId);

            // 方法1: 尝试从工作簿的所有图片数据中查找
            try {
                System.out.println("方法1: 检查工作簿中的所有图片数据");
                java.util.List<org.apache.poi.xssf.usermodel.XSSFPictureData> allPictures = workbook.getAllPictures();
                System.out.println("工作簿中共有" + allPictures.size() + "个图片数据");

                if (!allPictures.isEmpty()) {
                    // 如果有图片，尝试上传第一个（临时策略）
                    org.apache.poi.xssf.usermodel.XSSFPictureData firstPicture = allPictures.get(0);
                    System.out.println("尝试上传第一个图片，格式: " + firstPicture.suggestFileExtension());
                    return uploadXSSFPictureDataToMinio(firstPicture, imageId);
                }
            } catch (Exception e) {
                System.err.println("方法1失败: " + e.getMessage());
            }

            // 方法2: 遍历所有工作表的绘图对象
            System.out.println("方法2: 检查工作表绘图对象");
            for (int sheetIndex = 0; sheetIndex < workbook.getNumberOfSheets(); sheetIndex++) {
                org.apache.poi.xssf.usermodel.XSSFSheet sheet = workbook.getSheetAt(sheetIndex);
                System.out.println("检查工作表: " + sheet.getSheetName());

                // 获取工作表的绘图对象
                org.apache.poi.xssf.usermodel.XSSFDrawing drawing = sheet.getDrawingPatriarch();
                if (drawing != null) {
                    System.out.println("找到绘图对象，开始遍历形状");

                    // 遍历所有形状
                    int pictureCount = 0;
                    for (org.apache.poi.xssf.usermodel.XSSFShape shape : drawing.getShapes()) {
                        if (shape instanceof org.apache.poi.xssf.usermodel.XSSFPicture) {
                            pictureCount++;
                            org.apache.poi.xssf.usermodel.XSSFPicture picture =
                                (org.apache.poi.xssf.usermodel.XSSFPicture) shape;

                            System.out.println("找到第" + pictureCount + "个图片");

                            // 检查图片是否匹配ID（这里可能需要根据实际情况调整匹配逻辑）
                            if (isPictureMatchingId(picture, imageId)) {
                                System.out.println("图片匹配成功，开始上传");
                                return uploadXSSFPictureToMinio(picture);
                            }
                        }
                    }

                    System.out.println("工作表中共找到" + pictureCount + "个图片");
                } else {
                    System.out.println("工作表中没有绘图对象");
                }
            }

            System.out.println("未找到匹配的图片ID: " + imageId);
            return null;

        } catch (Exception e) {
            System.err.println("从XSSF工作簿提取图片失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 从HSSF工作簿中根据ID提取图片
     */
    private String extractImageFromHSSFWorkbook(HSSFWorkbook workbook, String imageId) {
        try {
            // 遍历所有工作表
            for (int sheetIndex = 0; sheetIndex < workbook.getNumberOfSheets(); sheetIndex++) {
                org.apache.poi.hssf.usermodel.HSSFSheet sheet = workbook.getSheetAt(sheetIndex);

                // 获取工作表的绘图对象
                org.apache.poi.hssf.usermodel.HSSFPatriarch patriarch = sheet.getDrawingPatriarch();
                if (patriarch != null) {
                    // 遍历所有形状
                    for (org.apache.poi.hssf.usermodel.HSSFShape shape : patriarch.getChildren()) {
                        if (shape instanceof org.apache.poi.hssf.usermodel.HSSFPicture) {
                            org.apache.poi.hssf.usermodel.HSSFPicture picture =
                                (org.apache.poi.hssf.usermodel.HSSFPicture) shape;

                            // 检查图片是否匹配ID
                            if (isPictureMatchingIdHSSF(picture, imageId)) {
                                return uploadHSSFPictureToMinio(picture);
                            }
                        }
                    }
                }
            }

            System.out.println("未找到匹配的图片ID: " + imageId);
            return null;

        } catch (Exception e) {
            System.err.println("从HSSF工作簿提取图片失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 检查XSSF图片是否匹配指定ID
     */
    private boolean isPictureMatchingId(org.apache.poi.xssf.usermodel.XSSFPicture picture, String imageId) {
        try {
            // 由于DISPIMG的ID可能存储在图片的属性中，我们先尝试简单的匹配
            // 这里可能需要根据实际的Excel文件结构进行调整

            // 方法1: 尝试获取图片的相关信息
            try {
                // 获取图片数据信息
                org.apache.poi.xssf.usermodel.XSSFPictureData pictureData = picture.getPictureData();
                String suggestedExtension = pictureData.suggestFileExtension();
                System.out.println("检查图片匹配 - 图片格式: " + suggestedExtension + ", 目标ID: " + imageId);
            } catch (Exception e) {
                System.out.println("获取图片信息失败: " + e.getMessage());
            }

            // 方法2: 由于DISPIMG是Excel的特殊功能，我们可能需要通过其他方式匹配
            // 暂时返回true来测试第一个图片
            System.out.println("检查图片匹配 - 目标ID: " + imageId);

            // 临时策略：如果只有一个图片，就认为匹配
            return true;

        } catch (Exception e) {
            System.err.println("检查图片匹配失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 检查HSSF图片是否匹配指定ID
     */
    private boolean isPictureMatchingIdHSSF(org.apache.poi.hssf.usermodel.HSSFPicture picture, String imageId) {
        try {
            // 类似XSSF的处理逻辑
            System.out.println("检查HSSF图片匹配 - 目标ID: " + imageId);

            // 临时策略：如果只有一个图片，就认为匹配
            return true;

        } catch (Exception e) {
            System.err.println("检查HSSF图片匹配失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 上传XSSF图片到MinIO
     */
    private String uploadXSSFPictureToMinio(org.apache.poi.xssf.usermodel.XSSFPicture picture) {
        try {
            org.apache.poi.xssf.usermodel.XSSFPictureData pictureData = picture.getPictureData();
            byte[] imageBytes = pictureData.getData();

            // 根据图片类型生成文件扩展名
            String extension = getImageExtension(pictureData.getPictureType());
            String fileName = "excel_dispimg_" + System.currentTimeMillis() + "_" +
                            java.util.UUID.randomUUID().toString().substring(0, 8) + extension;

            return uploadImageBytesToMinio(imageBytes, fileName, getContentTypeFromExtension(extension));

        } catch (Exception e) {
            System.err.println("上传XSSF图片到MinIO失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 上传HSSF图片到MinIO
     */
    private String uploadHSSFPictureToMinio(org.apache.poi.hssf.usermodel.HSSFPicture picture) {
        try {
            org.apache.poi.hssf.usermodel.HSSFPictureData pictureData = picture.getPictureData();
            byte[] imageBytes = pictureData.getData();

            // 根据图片格式生成文件扩展名
            String extension = getImageExtensionHSSF((short) pictureData.getFormat());
            String fileName = "excel_dispimg_" + System.currentTimeMillis() + "_" +
                            java.util.UUID.randomUUID().toString().substring(0, 8) + extension;

            return uploadImageBytesToMinio(imageBytes, fileName, getContentTypeFromExtension(extension));

        } catch (Exception e) {
            System.err.println("上传HSSF图片到MinIO失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 上传图片字节数组到MinIO
     */
    private String uploadImageBytesToMinio(byte[] imageBytes, String fileName, String contentType) {
        try {
            // 检查存储桶是否存在
            boolean isExist = minioClient.bucketExists(BucketExistsArgs.builder()
                    .bucket(minioConfig.getBucket())
                    .build());
            if (!isExist) {
                System.err.println("MinIO存储桶不存在: " + minioConfig.getBucket());
                return null;
            }

            // 上传文件到MinIO
            try (ByteArrayInputStream inputStream = new ByteArrayInputStream(imageBytes)) {
                minioClient.putObject(
                        PutObjectArgs.builder()
                                .bucket(minioConfig.getBucket())
                                .object(fileName)
                                .contentType(contentType)
                                .stream(inputStream, imageBytes.length, -1)
                                .build()
                );

                // 生成访问URL
                String fileUrl = minioClient.getObjectUrl(minioConfig.getBucket(), fileName);
                String cleanUrl = fileUrl.split("\\?")[0];

                System.out.println("Excel DISPIMG图片上传成功: " + cleanUrl);
                return cleanUrl;
            }

        } catch (Exception e) {
            System.err.println("上传图片字节到MinIO失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 根据图片类型获取文件扩展名
     */
    private String getImageExtension(int pictureType) {
        switch (pictureType) {
            case 5: // PICTURE_TYPE_JPEG
                return ".jpg";
            case 6: // PICTURE_TYPE_PNG
                return ".png";
            case 8: // PICTURE_TYPE_GIF
                return ".gif";
            case 9: // PICTURE_TYPE_BMP
                return ".bmp";
            default:
                return ".jpg"; // 默认为jpg
        }
    }

    /**
     * 根据HSSF图片格式获取文件扩展名
     */
    private String getImageExtensionHSSF(short format) {
        switch (format) {
            case 5: // PICTURE_TYPE_JPEG
                return ".jpg";
            case 6: // PICTURE_TYPE_PNG
                return ".png";
            case 8: // PICTURE_TYPE_GIF
                return ".gif";
            case 9: // PICTURE_TYPE_BMP
                return ".bmp";
            default:
                return ".jpg"; // 默认为jpg
        }
    }

    /**
     * 根据文件扩展名获取Content-Type
     */
    private String getContentTypeFromExtension(String extension) {
        switch (extension.toLowerCase()) {
            case ".jpg":
            case ".jpeg":
                return "image/jpeg";
            case ".png":
                return "image/png";
            case ".gif":
                return "image/gif";
            case ".bmp":
                return "image/bmp";
            default:
                return "image/jpeg";
        }
    }

    /**
     * 直接上传XSSF图片数据到MinIO
     */
    private String uploadXSSFPictureDataToMinio(org.apache.poi.xssf.usermodel.XSSFPictureData pictureData, String imageId) {
        try {
            byte[] imageBytes = pictureData.getData();

            // 根据图片类型生成文件扩展名
            String extension = "." + pictureData.suggestFileExtension();
            String fileName = "excel_dispimg_" + imageId + "_" + System.currentTimeMillis() + extension;

            String contentType = getContentTypeFromExtension(extension);

            System.out.println("准备上传图片: " + fileName + ", 大小: " + imageBytes.length + " bytes, 类型: " + contentType);

            return uploadImageBytesToMinio(imageBytes, fileName, contentType);

        } catch (Exception e) {
            System.err.println("上传XSSF图片数据到MinIO失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 验证导入数据
     */
    private void validateImportData(ImportResultDto result) {
        for (PointsApplyImportDto dto : result.getDetails()) {
            if (!dto.getIsValid()) {
                continue; // 已经有错误的跳过
            }

            List<String> errors = new ArrayList<>();

            // 验证必填字段
            if (dto.getStudentNo() == null || dto.getStudentNo().trim().isEmpty()) {
                errors.add("学号不能为空");
            }

            if (dto.getStudentName() == null || dto.getStudentName().trim().isEmpty()) {
                errors.add("姓名不能为空");
            }

            if (dto.getClassName() == null || dto.getClassName().trim().isEmpty()) {
                errors.add("班级不能为空");
            }

            if (dto.getPointsChange() == null) {
                errors.add("申请类型必须是'加分'或'减分'");
            }

            if (dto.getPoints() == null) {
                errors.add("分值不能为空");
            }

            if (dto.getReason() == null || dto.getReason().trim().isEmpty()) {
                errors.add("申请原因不能为空");
            }

            // 验证分值范围
            if (dto.getPoints() != null && dto.getPointsChange() != null) {
                if (dto.getPointsChange() == 1 && (dto.getPoints() < 1 || dto.getPoints() > 100)) {
                    errors.add("加分分值必须在1-100之间");
                } else if (dto.getPointsChange() == 2 && (dto.getPoints() < 1 || dto.getPoints() > 50)) {
                    errors.add("减分分值必须在1-50之间");
                }
            }

            // 验证学号和班级是否存在
            if (dto.getStudentNo() != null && !dto.getStudentNo().trim().isEmpty()) {
                try {
                    Map<String, Object> studentInfo = pointsApplyMapper.getStudentInfoByStudentNo(dto.getStudentNo().trim());
                    if (studentInfo == null) {
                        errors.add("学号不存在");
                    } else {
                        // 验证姓名是否匹配
                        String realName = (String) studentInfo.get("realName");
                        if (dto.getStudentName() != null && !dto.getStudentName().trim().equals(realName)) {
                            errors.add("姓名与学号不匹配，系统中该学号对应姓名为: " + realName);
                        }

                        // 验证班级是否匹配
                        String realClassName = (String) studentInfo.get("className");
                        if (dto.getClassName() != null && !dto.getClassName().trim().equals(realClassName)) {
                            errors.add("班级与学号不匹配，系统中该学号对应班级为: " + realClassName);
                        }

                        // 设置验证后的信息
                        dto.setRealStudentName(realName);
                        dto.setClassId((Integer) studentInfo.get("classId"));
                    }
                } catch (Exception e) {
                    errors.add("验证学号时出错: " + e.getMessage());
                }
            }

            // 设置验证结果
            if (!errors.isEmpty()) {
                dto.setIsValid(false);
                dto.setErrorMessage(String.join("; ", errors));
            }
        }
    }

    /**
     * 批量提交积分申请
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result batchSubmitPointsApply(List<PointsApplyImportDto> importList) {
        try {
            // 获取当前用户信息
            Integer currentUserId = UserContext.getCurrentUserId();
            Date currentTime = new Date();

            List<PointsApply> pointsApplyList = new ArrayList<>();
            int successCount = 0;
            int failCount = 0;
            List<String> errorMessages = new ArrayList<>();

            for (PointsApplyImportDto importDto : importList) {
                try {
                    // 只处理验证通过的数据
                    if (!importDto.getIsValid()) {
                        failCount++;
                        errorMessages.add("第" + importDto.getRowIndex() + "行: " + importDto.getErrorMessage());
                        continue;
                    }

                    // 创建PointsApply对象
                    PointsApply pointsApply = new PointsApply();
                    pointsApply.setStudentNo(importDto.getStudentNo());
                    pointsApply.setClassId(importDto.getClassId());
                    pointsApply.setPointsChange(importDto.getPointsChange());
                    pointsApply.setPoints(importDto.getPoints());
                    pointsApply.setReason(importDto.getReason());

                    // 设置证明素材说明（如果有的话）
                    if (importDto.getEvidenceMaterial() != null && !importDto.getEvidenceMaterial().trim().isEmpty()) {
                        String evidenceMaterial = importDto.getEvidenceMaterial().trim();
                        // 检查是否是有效的MinIO URL或文本说明
                        if (evidenceMaterial.startsWith("http://") || evidenceMaterial.startsWith("https://") ||
                            evidenceMaterial.contains("minio") || !evidenceMaterial.contains("://")) {
                            // 如果是URL或普通文本说明，则设置
                            pointsApply.setEvidenceImages(evidenceMaterial);
                        } else {
                            // 如果不是有效格式，设置为空
                            pointsApply.setEvidenceImages("");
                        }
                    } else {
                        pointsApply.setEvidenceImages("");
                    }

                    pointsApply.setApplyUserId(currentUserId);
                    pointsApply.setCreateBy(currentUserId);
                    pointsApply.setCreateTime(currentTime);
                    pointsApply.setDelFlag(1);

                    // 设置审核状态（根据当前用户角色）
                    setInitialApprovalStatus(pointsApply);

                    pointsApplyList.add(pointsApply);
                    successCount++;

                } catch (Exception e) {
                    failCount++;
                    errorMessages.add("第" + importDto.getRowIndex() + "行处理失败: " + e.getMessage());
                }
            }

            // 批量插入数据库
            if (!pointsApplyList.isEmpty()) {
                boolean batchResult = saveBatch(pointsApplyList, 1000);
                if (!batchResult) {
                    throw new Exception("批量保存到数据库失败");
                }
            }

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("totalCount", importList.size());
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("errorMessages", errorMessages);

            if (failCount > 0) {
                Result response = Result.OK(result);
                response.setMessage("批量提交完成，成功" + successCount + "条，失败" + failCount + "条");
                return response;
            } else {
                Result response = Result.OK(result);
                response.setMessage("批量提交成功，共处理" + successCount + "条记录");
                return response;
            }

        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("批量提交失败: " + e.getMessage());
        }
    }
}
