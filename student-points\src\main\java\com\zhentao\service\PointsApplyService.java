package com.zhentao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zhentao.dto.ImportResultDto;
import com.zhentao.dto.PointsApplyImportDto;
import com.zhentao.pojo.PointsApply;
import com.zhentao.utils.Result;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【points_apply】的数据库操作Service
 * @createDate 2025-07-07 18:59:20
 */
public interface PointsApplyService extends IService<PointsApply> {

    Result addJiFen(@RequestBody PointsApply pointsApply);

    Result listPointsApplies(Map<String, Object> params);

    /**
     * 获取积分历史记录，支持多种过滤条件
     * @param params 过滤参数
     * @return 积分历史记录列表
     */
    Result getPointsHistory(PointsApply pointsApply);

    /**
     * 获取积分统计信息
     * @param params 过滤参数
     * @return 积分统计信息
     */
    Result getPointsStatistics(Map<String, Object> params);

    /**
     * 撤销积分申请
     * @param applyId 申请ID
     * @return 操作结果
     */
    Result cancelPointsApplication(Integer applyId);

    /**
     * 查询当天加加分记录
     */
    public Result queryTodayAddPoints();
    /**
     * 查询当天的减分记录
     */
    public Result queryTodayMinusPoints();

    /**
     * 下载积分申请导入模板
     * @param response HTTP响应
     */
    void downloadPointsApplyTemplate(HttpServletResponse response) throws Exception;

    /**
     * 批量导入积分申请（解析Excel文件）
     * @param file Excel文件
     * @return 导入结果
     */
    ImportResultDto batchImportPointsApply(MultipartFile file) throws Exception;

    /**
     * 批量提交积分申请
     * @param importList 导入数据列表
     * @return 提交结果
     */
    Result batchSubmitPointsApply(List<PointsApplyImportDto> importList);

}
