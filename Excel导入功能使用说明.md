# Excel导入功能使用说明

## 功能概述

为ApplicationPage.vue页面添加了Excel批量导入积分申请的功能，秘书部可以通过Excel文件批量导入多个学生的积分申请，大大提高工作效率。

## 功能特性

### 1. Excel模板下载
- 点击"下载导入模板"按钮可下载标准Excel模板
- 模板包含必要字段：学号、姓名、班级、申请类型、分值、申请原因
- 提供示例数据行，方便用户理解填写格式

### 2. 文件上传和解析
- 支持.xlsx和.xls格式的Excel文件
- 文件大小限制：最大10MB
- 自动解析Excel内容并进行数据验证

### 3. 数据验证
- **学号验证**: 检查学号是否存在于系统中
- **姓名验证**: 验证姓名与学号是否匹配
- **班级验证**: 验证班级名称是否存在且与学号匹配
- **申请类型验证**: 只允许"加分"或"减分"
- **分值验证**: 加分1-100分，减分1-50分
- **原因验证**: 申请原因不能为空

### 4. 结果展示
- **统计信息**: 显示总记录数、成功记录数、失败记录数、处理耗时
- **错误汇总**: 列出导入过程中发现的问题
- **详细表格**: 显示每条记录的详细信息和验证状态
- **状态标识**: 用颜色区分验证通过和失败的记录

### 5. 批量提交
- 只提交验证通过的记录
- 支持预览确认后批量提交
- 自动刷新申请记录列表

## 使用流程

### 步骤1: 下载模板
1. 在ApplicationPage页面找到"批量导入申请"卡片
2. 点击"下载导入模板"按钮
3. 保存Excel模板文件到本地

### 步骤2: 填写数据
1. 打开下载的Excel模板
2. 按照表头要求填写学生信息：
   - **学号**: 学生的完整学号
   - **姓名**: 学生的真实姓名
   - **班级**: 学生所在班级的完整名称
   - **申请类型**: 填写"加分"或"减分"
   - **分值**: 填写具体的分值数字
   - **申请原因**: 详细描述申请原因

### 步骤3: 上传文件
1. 点击"选择Excel文件"按钮
2. 选择填写好的Excel文件
3. 系统会显示已选择的文件信息

### 步骤4: 导入解析
1. 点击"开始导入"按钮
2. 系统自动解析Excel文件并验证数据
3. 查看导入结果统计和详细信息

### 步骤5: 批量提交
1. 检查验证结果，确认数据无误
2. 点击"批量提交"按钮
3. 确认提交后，系统创建积分申请记录
4. 查看申请记录列表中的新增记录

## 注意事项

### 数据格式要求
1. **学号**: 必须是系统中存在的学号
2. **姓名**: 必须与学号对应的学生姓名完全一致
3. **班级**: 必须是系统中存在的班级名称，且与学号匹配
4. **申请类型**: 只能填写"加分"或"减分"，不能有其他文字
5. **分值**: 必须是正整数，加分1-100，减分1-50
6. **申请原因**: 不能为空，建议详细描述

### 常见错误及解决方法
1. **学号不存在**: 检查学号是否正确，确保学生已在系统中
2. **姓名不匹配**: 确保姓名与系统中的学生姓名完全一致
3. **班级不匹配**: 确保班级名称正确且与学生学号匹配
4. **申请类型错误**: 只能填写"加分"或"减分"
5. **分值超出范围**: 检查分值是否在允许范围内
6. **文件格式错误**: 确保使用.xlsx或.xls格式

### 权限说明
- 秘书角色可以为任何学生申请积分
- 其他角色可能有权限限制，只能为特定范围的学生申请
- 所有导入的申请都需要经过三级审核流程

## 技术实现

### 后端接口
- `GET /points-apply/downloadTemplate`: 下载Excel模板
- `POST /points-apply/batchImport`: 批量导入解析
- `POST /points-apply/batchSubmit`: 批量提交申请

### 前端组件
- 基于Element Plus的上传组件
- 响应式设计，支持移动端使用
- 实时数据验证和错误提示

### 数据处理
- 支持Excel 2003(.xls)和Excel 2007+(.xlsx)格式
- 自动处理数字、文本、日期等不同数据类型
- 批量数据验证和错误收集

## 故障排除

如果遇到问题，请检查：
1. 网络连接是否正常
2. 用户是否有相应权限
3. Excel文件格式是否正确
4. 数据填写是否符合要求
5. 浏览器控制台是否有错误信息

如需技术支持，请联系系统管理员。
