package com.zhentao.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhentao.pojo.*;
import com.zhentao.service.*;
import com.zhentao.utils.Result;
import com.zhentao.utils.UserContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import jakarta.servlet.http.HttpServletRequest;

import java.time.LocalDateTime;
import java.util.*;
import java.util.Date;

/**
 * <p>
 * 文书积分申请前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-07
 */
@RestController
@RequestMapping("/cultural-secretary-apply")
public class CulturalSecretaryApplyController {

    @Autowired
    private CulturalSecretaryApplyService culturalSecretaryApplyService;

    @Autowired
    private EduStudentService eduStudentService;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private EduClassService eduClassService;

    @Autowired
    private PointsRecordService pointsRecordService;

    @Autowired
    private OperationLogService operationLogService;

    @Autowired
    private SysUserRoleService sysUserRoleService;

    /**
     * 文书提交积分申请
     * @param apply 申请信息
     * @param request HTTP请求
     * @return 操作结果
     */
    @PostMapping("/submit")
    public Result submitApply(@RequestBody CulturalSecretaryApply apply, HttpServletRequest request) {
        try {
            // 获取当前用户信息
            Integer currentUserId = UserContext.getCurrentUser().getUserId();
            SysUser currentUser = sysUserService.getById(currentUserId);

            // 验证当前用户是否为文书角色（role_id = 8）
            if (!hasRole(currentUserId, 8)) {
                return Result.ERROR("只有文书可以提交积分申请");
            }

            // 验证学生是否存在
            LambdaQueryWrapper<EduStudent> studentWrapper = new LambdaQueryWrapper<>();
            studentWrapper.eq(EduStudent::getStudentNo, apply.getStudentNo());
            EduStudent student = eduStudentService.getOne(studentWrapper);
            if (student == null) {
                return Result.ERROR("学生不存在");
            }

            // 验证文书是否有权限为该学生申请积分（同班级）
            if (!canApplyForStudent(currentUserId, student.getClassId())) {
                return Result.ERROR("您只能为本班学生申请积分");
            }

            // 设置申请基本信息
            apply.setApplyUserId(currentUserId);
            apply.setClassId(student.getClassId());
            apply.setStatus(1); // 待审核
            apply.setCreateBy(currentUserId);
            apply.setCreateTime(LocalDateTime.now());
            apply.setDelFlag(1);

            // 保存申请
            boolean success = culturalSecretaryApplyService.save(apply);

            // 记录操作日志
            if (success) {
                String operationType = apply.getPointsChange() == 2 ? "扣分" : "加分";
                String operationDescription = String.format("文书积分申请：学号=%s，%s%d分，原因=%s",
                    apply.getStudentNo(), operationType, apply.getPoints(), apply.getReason());

                operationLogService.recordLog(currentUser.getUsername(), currentUser.getRealName(),
                    3, "cultural_secretary_apply", operationDescription, 1, request);
            }

            return success ? Result.OK("申请提交成功") : Result.ERROR("申请提交失败");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("提交申请失败: " + e.getMessage());
        }
    }

    /**
     * 文书查看自己提交的申请列表
     * @param params 查询参数
     * @return 申请列表
     */
    @PostMapping("/myApplications")
    public Result getMyApplications(@RequestBody Map<String, Object> params) {
        try {
            Integer currentUserId = UserContext.getCurrentUser().getUserId();

            // 验证当前用户是否为文书角色
            if (!hasRole(currentUserId, 8)) {
                return Result.ERROR("只有文书可以查看申请记录");
            }

            // 分页参数
            int pageNum = params.containsKey("pageNum") ?
                Integer.parseInt(params.get("pageNum").toString()) : 1;
            int pageSize = params.containsKey("pageSize") ?
                Integer.parseInt(params.get("pageSize").toString()) : 10;

            // 构建查询条件
            LambdaQueryWrapper<CulturalSecretaryApply> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CulturalSecretaryApply::getApplyUserId, currentUserId);
            wrapper.eq(CulturalSecretaryApply::getDelFlag, 1);

            // 状态筛选
            if (params.containsKey("status") && params.get("status") != null) {
                wrapper.eq(CulturalSecretaryApply::getStatus, params.get("status"));
            }

            // 积分变动类型筛选
            if (params.containsKey("pointsChange") && params.get("pointsChange") != null) {
                wrapper.eq(CulturalSecretaryApply::getPointsChange, params.get("pointsChange"));
            }

            // 时间范围筛选
            if (params.containsKey("startTime") && params.get("startTime") != null) {
                wrapper.ge(CulturalSecretaryApply::getCreateTime, params.get("startTime"));
            }
            if (params.containsKey("endTime") && params.get("endTime") != null) {
                wrapper.le(CulturalSecretaryApply::getCreateTime, params.get("endTime"));
            }

            wrapper.orderByDesc(CulturalSecretaryApply::getCreateTime);

            // 执行分页查询
            Page<CulturalSecretaryApply> page = new Page<>(pageNum, pageSize);
            Page<CulturalSecretaryApply> result = culturalSecretaryApplyService.page(page, wrapper);

            // 填充关联信息
            for (CulturalSecretaryApply apply : result.getRecords()) {
                enrichApplyInfo(apply);
            }

            return Result.OK(result);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("查询失败: " + e.getMessage());
        }
    }

    /**
     * 秘书部查看待审核的申请列表
     * @param params 查询参数
     * @return 申请列表
     */
    @PostMapping("/pendingApplications")
    public Result getPendingApplications(@RequestBody Map<String, Object> params) {
        try {
            Integer currentUserId = UserContext.getCurrentUser().getUserId();

            // 验证当前用户是否为秘书角色（role_id = 6）
            if (!hasRole(currentUserId, 6)) {
                return Result.ERROR("只有秘书可以查看待审核申请");
            }

            // 分页参数
            int pageNum = params.containsKey("pageNum") ?
                Integer.parseInt(params.get("pageNum").toString()) : 1;
            int pageSize = params.containsKey("pageSize") ?
                Integer.parseInt(params.get("pageSize").toString()) : 10;

            // 构建查询条件
            LambdaQueryWrapper<CulturalSecretaryApply> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CulturalSecretaryApply::getDelFlag, 1);

            // 状态筛选
            if (params.containsKey("status") && params.get("status") != null) {
                wrapper.eq(CulturalSecretaryApply::getStatus, params.get("status"));
            } else {
                wrapper.eq(CulturalSecretaryApply::getStatus, 1); // 默认查看待审核
            }

            // 积分变动类型筛选
            if (params.containsKey("pointsChange") && params.get("pointsChange") != null) {
                wrapper.eq(CulturalSecretaryApply::getPointsChange, params.get("pointsChange"));
            }

            // 班级筛选
            if (params.containsKey("classId") && params.get("classId") != null) {
                wrapper.eq(CulturalSecretaryApply::getClassId, params.get("classId"));
            }

            // 时间范围筛选
            if (params.containsKey("startTime") && params.get("startTime") != null) {
                wrapper.ge(CulturalSecretaryApply::getCreateTime, params.get("startTime"));
            }
            if (params.containsKey("endTime") && params.get("endTime") != null) {
                wrapper.le(CulturalSecretaryApply::getCreateTime, params.get("endTime"));
            }

            wrapper.orderByDesc(CulturalSecretaryApply::getCreateTime);

            // 执行分页查询
            Page<CulturalSecretaryApply> page = new Page<>(pageNum, pageSize);
            Page<CulturalSecretaryApply> result = culturalSecretaryApplyService.page(page, wrapper);

            // 填充关联信息
            for (CulturalSecretaryApply apply : result.getRecords()) {
                enrichApplyInfo(apply);
            }

            return Result.OK(result);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("查询失败: " + e.getMessage());
        }
    }

    /**
     * 秘书审核申请（通过）
     * @param id 申请ID
     * @param reviewComment 审核意见
     * @param request HTTP请求
     * @return 操作结果
     */
    @PostMapping("/approve/{id}")
    public Result approveApplication(@PathVariable Integer id,
                                   @RequestParam(required = false) String reviewComment,
                                   HttpServletRequest request) {
        try {
            Integer currentUserId = UserContext.getCurrentUser().getUserId();
            SysUser currentUser = sysUserService.getById(currentUserId);

            // 验证当前用户是否为秘书角色
            if (!hasRole(currentUserId, 6)) {
                return Result.ERROR("只有秘书可以审核申请");
            }

            // 获取申请信息
            CulturalSecretaryApply apply = culturalSecretaryApplyService.getById(id);
            if (apply == null) {
                return Result.ERROR("申请不存在");
            }

            if (apply.getStatus() != 1) {
                return Result.ERROR("该申请已被处理");
            }

            // 获取学生信息
            LambdaQueryWrapper<EduStudent> studentWrapper = new LambdaQueryWrapper<>();
            studentWrapper.eq(EduStudent::getStudentNo, apply.getStudentNo());
            EduStudent student = eduStudentService.getOne(studentWrapper);

            if (student == null) {
                return Result.ERROR("学生不存在");
            }

            // 检查学生状态
            if (!"0".equals(student.getStatus())) {
                return Result.ERROR("该学生状态异常，无法进行积分操作");
            }

            if (!"0".equals(student.getDelFlag())) {
                return Result.ERROR("该学生已删除，无法进行积分操作");
            }

            // 更新申请状态
            apply.setStatus(2); // 已通过
            apply.setReviewerId(currentUserId);
            apply.setReviewTime(LocalDateTime.now());
            apply.setReviewComment(reviewComment);
            apply.setUpdateBy(currentUserId);
            apply.setUpdateTime(LocalDateTime.now());

            boolean updateSuccess = culturalSecretaryApplyService.updateById(apply);

            if (updateSuccess) {
                // 更新学生积分
                Integer pointsBefore = student.getPoints();
                Integer pointsAfter;

                if (apply.getPointsChange() == 1) {
                    // 加分
                    pointsAfter = pointsBefore + apply.getPoints();
                } else {
                    // 减分
                    pointsAfter = Math.max(0, pointsBefore - apply.getPoints());
                }

                student.setPoints(pointsAfter);
                student.setUpdateBy(currentUserId);
                student.setUpdateTime(new Date());
                eduStudentService.updateById(student);

                // 记录积分变动
                PointsRecord pointsRecord = new PointsRecord();
                pointsRecord.setStudentNo(apply.getStudentNo());
                pointsRecord.setPointsBefore(pointsBefore);
                pointsRecord.setPointsChange(apply.getPointsChange());
                pointsRecord.setPointsAfter(pointsAfter);
                pointsRecord.setApplyId(apply.getId());
                pointsRecord.setOperatorId(currentUserId);
                pointsRecord.setOperationType(6); // 文书申请审核通过
                pointsRecord.setOperationTime(new Date());
                pointsRecord.setReason(apply.getReason());
                pointsRecord.setCreateBy(currentUserId);
                pointsRecord.setCreateTime(new Date());
                pointsRecord.setDelFlag(1);
                pointsRecordService.save(pointsRecord);

                // 记录操作日志
                String operationType = apply.getPointsChange() == 2 ? "扣分" : "加分";
                String operationDescription = String.format("秘书审核通过文书申请：申请ID=%d，学号=%s，%s%d分",
                    id, apply.getStudentNo(), operationType, apply.getPoints());

                operationLogService.recordLog(currentUser.getUsername(), currentUser.getRealName(),
                    10, "cultural_secretary_apply", operationDescription, 1, request);
            }

            return updateSuccess ? Result.OK("审核通过成功") : Result.ERROR("审核失败");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("审核失败: " + e.getMessage());
        }
    }

    /**
     * 秘书审核申请（拒绝）
     * @param id 申请ID
     * @param reviewComment 审核意见
     * @param request HTTP请求
     * @return 操作结果
     */
    @PostMapping("/reject/{id}")
    public Result rejectApplication(@PathVariable Integer id,
                                  @RequestParam(required = false) String reviewComment,
                                  HttpServletRequest request) {
        try {
            Integer currentUserId = UserContext.getCurrentUser().getUserId();
            SysUser currentUser = sysUserService.getById(currentUserId);

            // 验证当前用户是否为秘书角色
            if (!hasRole(currentUserId, 6)) {
                return Result.ERROR("只有秘书可以审核申请");
            }

            // 获取申请信息
            CulturalSecretaryApply apply = culturalSecretaryApplyService.getById(id);
            if (apply == null) {
                return Result.ERROR("申请不存在");
            }

            if (apply.getStatus() != 1) {
                return Result.ERROR("该申请已被处理");
            }

            // 更新申请状态
            apply.setStatus(3); // 已拒绝
            apply.setReviewerId(currentUserId);
            apply.setReviewTime(LocalDateTime.now());
            apply.setReviewComment(reviewComment);
            apply.setUpdateBy(currentUserId);
            apply.setUpdateTime(LocalDateTime.now());

            boolean updateSuccess = culturalSecretaryApplyService.updateById(apply);

            if (updateSuccess) {
                // 记录操作日志
                String operationType = apply.getPointsChange() == 2 ? "扣分" : "加分";
                String operationDescription = String.format("秘书审核拒绝文书申请：申请ID=%d，学号=%s，%s%d分",
                    id, apply.getStudentNo(), operationType, apply.getPoints());

                operationLogService.recordLog(currentUser.getUsername(), currentUser.getRealName(),
                    10, "cultural_secretary_apply", operationDescription, 1, request);
            }

            return updateSuccess ? Result.OK("审核拒绝成功") : Result.ERROR("审核失败");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("审核失败: " + e.getMessage());
        }
    }

    /**
     * 撤销申请
     * @param id 申请ID
     * @param request HTTP请求
     * @return 操作结果
     */
    @PostMapping("/withdraw/{id}")
    public Result withdrawApplication(@PathVariable Integer id, HttpServletRequest request) {
        try {
            Integer currentUserId = UserContext.getCurrentUser().getUserId();
            SysUser currentUser = sysUserService.getById(currentUserId);

            // 获取申请信息
            CulturalSecretaryApply apply = culturalSecretaryApplyService.getById(id);
            if (apply == null) {
                return Result.ERROR("申请不存在");
            }

            // 验证权限：只有申请人可以撤销
            if (!apply.getApplyUserId().equals(currentUserId)) {
                return Result.ERROR("只能撤销自己的申请");
            }

            if (apply.getStatus() != 1) {
                return Result.ERROR("只能撤销待审核的申请");
            }

            // 更新申请状态
            apply.setStatus(4); // 已撤销
            apply.setUpdateBy(currentUserId);
            apply.setUpdateTime(LocalDateTime.now());

            boolean updateSuccess = culturalSecretaryApplyService.updateById(apply);

            if (updateSuccess) {
                // 记录操作日志
                String operationType = apply.getPointsChange() == 2 ? "扣分" : "加分";
                String operationDescription = String.format("撤销文书申请：申请ID=%d，学号=%s，%s%d分",
                    id, apply.getStudentNo(), operationType, apply.getPoints());

                operationLogService.recordLog(currentUser.getUsername(), currentUser.getRealName(),
                    5, "cultural_secretary_apply", operationDescription, 1, request);
            }

            return updateSuccess ? Result.OK("撤销成功") : Result.ERROR("撤销失败");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("撤销失败: " + e.getMessage());
        }
    }

    /**
     * 获取班级列表（供文书选择）
     * @return 班级列表
     */
    @GetMapping("/classes")
    public Result getClasses() {
        try {
            Integer currentUserId = UserContext.getCurrentUser().getUserId();

            // 验证当前用户是否为文书角色
            if (!hasRole(currentUserId, 8)) {
                return Result.ERROR("只有文书可以查看班级信息");
            }

            // 获取文书所在的班级
            List<EduClass> classes = getClassesBySecretaryId(currentUserId);

            return Result.OK(classes);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("查询班级失败: " + e.getMessage());
        }
    }

    /**
     * 测试接口：获取文书信息和班级信息
     * @return 文书信息
     */
    @GetMapping("/test/secretary-info")
    public Result getSecretaryInfo() {
        try {
            Integer currentUserId = UserContext.getCurrentUser().getUserId();
            SysUser currentUser = sysUserService.getById(currentUserId);

            Map<String, Object> result = new HashMap<>();
            result.put("userId", currentUserId);
            result.put("username", currentUser.getUsername());
            result.put("realName", currentUser.getRealName());
            result.put("userType", currentUser.getUserType());

            // 获取文书对应的学生信息
            LambdaQueryWrapper<EduStudent> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(EduStudent::getStudentNo, currentUser.getUsername());
            wrapper.eq(EduStudent::getDelFlag, "0");
            EduStudent student = eduStudentService.getOne(wrapper);

            if (student != null) {
                result.put("studentInfo", student);
                result.put("classId", student.getClassId());

                // 获取班级信息
                EduClass eduClass = eduClassService.getById(student.getClassId());
                result.put("classInfo", eduClass);

                // 获取同班级学生列表
                LambdaQueryWrapper<EduStudent> classWrapper = new LambdaQueryWrapper<>();
                classWrapper.eq(EduStudent::getClassId, student.getClassId());
                classWrapper.eq(EduStudent::getDelFlag, "0");
                classWrapper.eq(EduStudent::getStatus, "0");
                List<EduStudent> classmates = eduStudentService.list(classWrapper);
                result.put("classmates", classmates);
            } else {
                result.put("error", "未找到对应的学生信息，请确保用户名与学号一致");
            }

            return Result.OK(result);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("获取信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据班级ID获取学生列表
     * @param classId 班级ID
     * @return 学生列表
     */
    @GetMapping("/students/{classId}")
    public Result getStudentsByClass(@PathVariable Integer classId) {
        try {
            Integer currentUserId = UserContext.getCurrentUser().getUserId();

            // 验证当前用户是否为文书角色
            if (!hasRole(currentUserId, 8)) {
                return Result.ERROR("只有文书可以查看学生信息");
            }

            // 验证文书是否有权限查看该班级学生
            if (!canApplyForStudent(currentUserId, classId)) {
                return Result.ERROR("您只能查看本班学生");
            }

            // 查询班级学生
            LambdaQueryWrapper<EduStudent> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(EduStudent::getClassId, classId);
            wrapper.eq(EduStudent::getDelFlag, "0");
            wrapper.eq(EduStudent::getStatus, "0");
            wrapper.orderBy(true, true, EduStudent::getStudentNo);

            List<EduStudent> students = eduStudentService.list(wrapper);

            return Result.OK(students);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("查询学生失败: " + e.getMessage());
        }
    }

    // ================= 私有辅助方法 =================

    /**
     * 检查用户是否具有指定角色
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 是否具有角色
     */
    private boolean hasRole(Integer userId, Integer roleId) {
        try {
            // 查询用户角色关联表
            LambdaQueryWrapper<SysUserRole> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SysUserRole::getUserId, userId);
            wrapper.eq(SysUserRole::getRoleId, roleId);

            return sysUserRoleService.count(wrapper) > 0;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查文书是否可以为指定班级的学生申请积分
     * 通过学生表查找文书所在班级
     * @param secretaryUserId 文书用户ID
     * @param classId 班级ID
     * @return 是否有权限
     */
    private boolean canApplyForStudent(Integer secretaryUserId, Integer classId) {
        try {
            // 获取文书所在的班级ID
            Integer secretaryClassId = getSecretaryClassId(secretaryUserId);
            return secretaryClassId != null && secretaryClassId.equals(classId);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取文书所在的班级ID
     * 通过学生表查找文书对应的学生记录，获取班级ID
     * @param secretaryUserId 文书用户ID
     * @return 班级ID
     */
    private Integer getSecretaryClassId(Integer secretaryUserId) {
        try {
            // 获取文书用户信息
            SysUser secretary = sysUserService.getById(secretaryUserId);
            if (secretary == null) {
                return null;
            }

            // 通过用户名（假设用户名就是学号）查找学生记录
            LambdaQueryWrapper<EduStudent> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(EduStudent::getStudentNo, secretary.getUsername());
            wrapper.eq(EduStudent::getDelFlag, "0");
            EduStudent student = eduStudentService.getOne(wrapper);

            return student != null ? student.getClassId() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取文书所在的班级列表
     * @param secretaryUserId 文书用户ID
     * @return 班级列表
     */
    private List<EduClass> getClassesBySecretaryId(Integer secretaryUserId) {
        try {
            Integer classId = getSecretaryClassId(secretaryUserId);
            if (classId != null) {
                EduClass eduClass = eduClassService.getById(classId);
                if (eduClass != null) {
                    return Arrays.asList(eduClass);
                }
            }
            return new ArrayList<>();
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    /**
     * 填充申请信息的关联数据
     * @param apply 申请对象
     */
    private void enrichApplyInfo(CulturalSecretaryApply apply) {
        try {
            // 填充学生信息
            LambdaQueryWrapper<EduStudent> studentWrapper = new LambdaQueryWrapper<>();
            studentWrapper.eq(EduStudent::getStudentNo, apply.getStudentNo());
            EduStudent student = eduStudentService.getOne(studentWrapper);
            if (student != null) {
                apply.setStudent(student);
            }

            // 填充申请人信息
            if (apply.getApplyUserId() != null) {
                SysUser applyUser = sysUserService.getById(apply.getApplyUserId());
                apply.setApplyUser(applyUser);
            }

            // 填充审核人信息
            if (apply.getReviewerId() != null) {
                SysUser reviewer = sysUserService.getById(apply.getReviewerId());
                apply.setReviewer(reviewer);
            }

            // 填充班级信息
            if (apply.getClassId() != null) {
                EduClass eduClass = eduClassService.getById(apply.getClassId());
                apply.setEduClass(eduClass);
            }

            // 填充创建人信息
            if (apply.getCreateBy() != null) {
                SysUser createUser = sysUserService.getById(apply.getCreateBy());
                apply.setCreateUser(createUser);
            }

        } catch (Exception e) {
            System.err.println("填充申请信息失败: " + e.getMessage());
        }
    }
}
