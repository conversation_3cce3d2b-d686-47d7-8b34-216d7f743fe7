<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Delete, Download } from '@element-plus/icons-vue'

// 操作类型选项
const operationTypeOptions = [
  { value: 'login', label: '登录' },
  { value: 'logout', label: '登出' },
  { value: 'add', label: '新增' },
  { value: 'update', label: '修改' },
  { value: 'delete', label: '删除' },
  { value: 'query', label: '查询' },
  { value: 'import', label: '导入' },
  { value: 'export', label: '导出' },
  { value: 'approve', label: '审批' },
  { value: 'reject', label: '驳回' },
  { value: 'other', label: '其他' }
]

// 模块选项
const moduleOptions = [
  { value: 'system', label: '系统管理' },
  { value: 'user', label: '用户管理' },
  { value: 'role', label: '角色管理' },
  { value: 'student', label: '学生管理' },
  { value: 'class', label: '班级管理' },
  { value: 'points', label: '积分管理' },
  { value: 'approval', label: '审批管理' },
  { value: 'statistics', label: '统计分析' }
]

// 状态选项
const statusOptions = [
  { value: 'success', label: '成功' },
  { value: 'fail', label: '失败' }
]

// 搜索表单
const searchForm = reactive({
  username: '',
  operationType: '',
  module: '',
  status: '',
  startTime: '',
  endTime: ''
})

// 表格数据
const loading = ref(false)
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 详情对话框
const detailDialogVisible = ref(false)
const currentLog = ref(null)

// 生命周期钩子
onMounted(() => {
  fetchData()
})

// 方法定义
const fetchData = () => {
  loading.value = true
  // 模拟API请求
  setTimeout(() => {
    tableData.value = generateMockData()
    total.value = 200
    loading.value = false
  }, 500)
}

const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

const resetSearch = () => {
  searchForm.username = ''
  searchForm.operationType = ''
  searchForm.module = ''
  searchForm.status = ''
  searchForm.startTime = ''
  searchForm.endTime = ''
  currentPage.value = 1
  fetchData()
}

const handleSizeChange = (val) => {
  pageSize.value = val
  fetchData()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchData()
}

const handleRefresh = () => {
  fetchData()
}

const handleClear = () => {
  ElMessage.warning('此操作将清空所有日志，请谨慎操作')
}

const handleExport = () => {
  ElMessage.success('日志导出成功')
}

const showDetail = (row) => {
  currentLog.value = row
  detailDialogVisible.value = true
}

const getStatusType = (status) => {
  switch (status) {
    case 'success': return 'success'
    case 'fail': return 'danger'
    default: return 'info'
  }
}

const getOperationTypeLabel = (type) => {
  const option = operationTypeOptions.find(item => item.value === type)
  return option ? option.label : type
}

const getModuleLabel = (module) => {
  const option = moduleOptions.find(item => item.value === module)
  return option ? option.label : module
}

const getStatusLabel = (status) => {
  const option = statusOptions.find(item => item.value === status)
  return option ? option.label : status
}

// 格式化请求参数
const formatParams = (params) => {
  if (!params) return '-'
  try {
    const obj = JSON.parse(params)
    return JSON.stringify(obj, null, 2)
  } catch (e) {
    return params
  }
}

// 生成模拟数据
const generateMockData = () => {
  const data = []
  
  const usernames = ['admin', 'teacher1', 'teacher2', 'student1', 'student2']
  const ips = ['***********', '***********', '***********', '***********', '********']
  const browsers = ['Chrome', 'Firefox', 'Edge', 'Safari']
  const oss = ['Windows 10', 'Windows 11', 'macOS', 'Ubuntu']
  
  for (let i = 1; i <= 10; i++) {
    const operationType = operationTypeOptions[Math.floor(Math.random() * operationTypeOptions.length)].value
    const module = moduleOptions[Math.floor(Math.random() * moduleOptions.length)].value
    const status = statusOptions[Math.floor(Math.random() * statusOptions.length)].value
    const username = usernames[Math.floor(Math.random() * usernames.length)]
    const ip = ips[Math.floor(Math.random() * ips.length)]
    const browser = browsers[Math.floor(Math.random() * browsers.length)]
    const os = oss[Math.floor(Math.random() * oss.length)]
    
    let description = ''
    let params = '{}'
    
    switch (operationType) {
      case 'login':
        description = `用户 ${username} 登录系统`
        params = `{"username": "${username}", "ip": "${ip}", "browser": "${browser}"}`
        break
      case 'logout':
        description = `用户 ${username} 登出系统`
        params = `{"username": "${username}"}`
        break
      case 'add':
        if (module === 'user') {
          description = `新增用户: user${i}`
          params = `{"username": "user${i}", "role": "teacher"}`
        } else if (module === 'student') {
          description = `新增学生: student${i}`
          params = `{"name": "学生${i}", "studentId": "2023${i.toString().padStart(4, '0')}"}`
        } else {
          description = `在 ${getModuleLabel(module)} 模块执行新增操作`
        }
        break
      case 'update':
        if (module === 'user') {
          description = `修改用户: ${username}`
          params = `{"id": "${i}", "username": "${username}", "email": "${username}@example.com"}`
        } else if (module === 'points') {
          description = `修改学生积分: student${i}`
          params = `{"studentId": "2023${i.toString().padStart(4, '0')}", "points": 10, "reason": "表现良好"}`
        } else {
          description = `在 ${getModuleLabel(module)} 模块执行修改操作`
        }
        break
      case 'delete':
        description = `在 ${getModuleLabel(module)} 模块执行删除操作`
        params = `{"id": "${i}"}`
        break
      case 'query':
        description = `在 ${getModuleLabel(module)} 模块执行查询操作`
        params = `{"keyword": "test", "page": 1, "size": 10}`
        break
      default:
        description = `在 ${getModuleLabel(module)} 模块执行 ${getOperationTypeLabel(operationType)} 操作`
    }
    
    const date = new Date()
    date.setMinutes(date.getMinutes() - i * 10)
    
    data.push({
      id: i.toString(),
      username,
      operationType,
      module,
      description,
      ip,
      location: '中国 广东省 深圳市',
      browser: `${browser} ${Math.floor(Math.random() * 100)}`,
      os,
      status,
      params,
      errorMsg: status === 'fail' ? '操作失败，权限不足' : '',
      operationTime: date.toLocaleString()
    })
  }
  
  return data
}
</script>

<template>
  <div class="operation-log-container">
    <div class="page-header">
      <h2>操作日志</h2>
      <div class="header-actions">
        <el-button type="danger" :icon="Delete" @click="handleClear">清空日志</el-button>
        <el-button type="success" :icon="Download" @click="handleExport">导出日志</el-button>
        <el-button type="primary" :icon="Refresh" circle @click="handleRefresh" />
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户名">
          <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable />
        </el-form-item>
        
        <el-form-item label="操作类型">
          <el-select style="width: auto" v-model="searchForm.operationType"  placeholder="请选择操作类型" clearable>
            <el-option
              v-for="item in operationTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="操作模块">
          <el-select v-model="searchForm.module" placeholder="请选择操作模块" clearable>
            <el-option
              v-for="item in moduleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="操作时间">
          <el-date-picker
            v-model="searchForm.startTime"
            type="datetime"
            placeholder="开始时间"
            style="width: 180px"
          />
          <span class="date-separator">至</span>
          <el-date-picker
            v-model="searchForm.endTime"
            type="datetime"
            placeholder="结束时间"
            style="width: 180px"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container">
      <div class="table-scroll-area">
        <el-table
          v-loading="loading"
          :data="tableData"
          border
          style="width: 100%"
        >
          <el-table-column prop="username" label="用户名" width="100" />
          <el-table-column prop="operationType" label="操作类型" width="100">
            <template #default="scope">
              {{ getOperationTypeLabel(scope.row.operationType) }}
            </template>
          </el-table-column>
          <el-table-column prop="module" label="操作模块" width="100">
            <template #default="scope">
              {{ getModuleLabel(scope.row.module) }}
            </template>
          </el-table-column>
          <el-table-column prop="description" label="操作描述" min-width="200" show-overflow-tooltip />
          <el-table-column prop="ip" label="IP地址" width="120" />
          <el-table-column prop="status" label="状态" width="80">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusLabel(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="operationTime" label="操作时间" width="180" />
          <el-table-column label="操作" fixed="right" width="80">
            <template #default="scope">
              <el-button type="primary" link @click="showDetail(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:currentPage="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    
    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="日志详情"
      width="700px"
    >
      <el-descriptions :column="2" border v-if="currentLog">
        <el-descriptions-item label="用户名">{{ currentLog.username }}</el-descriptions-item>
        <el-descriptions-item label="操作类型">{{ getOperationTypeLabel(currentLog.operationType) }}</el-descriptions-item>
        <el-descriptions-item label="操作模块">{{ getModuleLabel(currentLog.module) }}</el-descriptions-item>
        <el-descriptions-item label="操作状态">
          <el-tag :type="getStatusType(currentLog.status)">
            {{ getStatusLabel(currentLog.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="IP地址">{{ currentLog.ip }}</el-descriptions-item>
        <el-descriptions-item label="地理位置">{{ currentLog.location }}</el-descriptions-item>
        <el-descriptions-item label="浏览器">{{ currentLog.browser }}</el-descriptions-item>
        <el-descriptions-item label="操作系统">{{ currentLog.os }}</el-descriptions-item>
        <el-descriptions-item label="操作时间">{{ currentLog.operationTime }}</el-descriptions-item>
        <el-descriptions-item label="操作描述" :span="2">{{ currentLog.description }}</el-descriptions-item>
        <el-descriptions-item label="请求参数" :span="2">
          <pre class="params-pre">{{ formatParams(currentLog.params) }}</pre>
        </el-descriptions-item>
        <el-descriptions-item v-if="currentLog.errorMsg" label="错误信息" :span="2" class="error-message">
          {{ currentLog.errorMsg }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<style scoped>
.operation-log-container {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.page-header {
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 22px;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-container {
  flex-shrink: 0;
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

.table-container {
  flex: 1;
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

.table-scroll-area {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.pagination-container {
  flex-shrink: 0;
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.params-pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.error-message {
  color: #F56C6C;
}

.date-separator {
  margin: 0 5px;
}
</style>