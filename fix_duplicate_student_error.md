# 修复重复学生记录导致的登录错误

## 错误描述
```
org.apache.ibatis.exceptions.TooManyResultsException: Expected one result (or null) to be returned by selectOne(), but found: 2
```

## 错误原因分析
1. **错误位置**: `SecurityConfig.java:139` 行的 `onAuthenticationSuccess` 方法
2. **错误原因**: 在用户登录成功后，系统尝试通过手机号和姓名查找学生记录时，发现了多个匹配的记录
3. **根本原因**: 我们之前的修改中自动创建了文书学生记录，可能导致数据库中存在重复的学生数据

## 问题代码
```java
// SecurityConfig.java 第139行
EduStudent student = eduStudentService.getOne(new QueryWrapper<EduStudent>()
        .eq("phone", sysUser.getPhone())
        .eq("real_name", sysUser.getRealName())
);
```

当数据库中有多个学生具有相同的手机号和姓名时，`getOne()` 方法会抛出 `TooManyResultsException`。

## 解决方案

### 1. 修改 SecurityConfig.java 中的查询逻辑
```java
EduStudent student = null;
try {
    // 首先尝试通过用户名（学号）查找
    student = eduStudentService.getOne(new QueryWrapper<EduStudent>()
            .eq("student_no", sysUser.getUsername())
            .eq("del_flag", "0")
            .last("LIMIT 1")
    );
    
    // 如果通过学号找不到，再尝试通过手机号和姓名查找
    if (student == null && sysUser.getPhone() != null && sysUser.getRealName() != null) {
        List<EduStudent> students = eduStudentService.list(new QueryWrapper<EduStudent>()
                .eq("phone", sysUser.getPhone())
                .eq("real_name", sysUser.getRealName())
                .eq("del_flag", "0")
        );
        if (!students.isEmpty()) {
            student = students.get(0); // 取第一个匹配的学生
            System.out.println("通过手机号和姓名找到学生，取第一个: " + student.getStudentNo());
        }
    }
} catch (Exception e) {
    System.err.println("查找学生信息时出错: " + e.getMessage());
    student = null;
}
```

### 2. 添加重复数据清理功能
在 `EduStudentServiceImpl.java` 中添加了 `cleanupDuplicateStudents` 方法：
```java
private void cleanupDuplicateStudents() {
    try {
        System.out.println("开始清理重复的学生数据...");
        
        // 查找文书用户的重复记录
        LambdaQueryWrapper<EduStudent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EduStudent::getStudentNo, "sunshanshan");
        wrapper.eq(EduStudent::getDelFlag, "0");
        wrapper.orderByDesc(EduStudent::getCreateTime);
        
        List<EduStudent> duplicates = list(wrapper);
        if (duplicates.size() > 1) {
            System.out.println("发现文书用户 sunshanshan 有 " + duplicates.size() + " 条重复记录，保留最新的一条");
            
            // 保留第一条（最新的），删除其他的
            for (int i = 1; i < duplicates.size(); i++) {
                EduStudent duplicate = duplicates.get(i);
                duplicate.setDelFlag(1); // 标记为删除
                updateById(duplicate);
                System.out.println("删除重复记录: ID=" + duplicate.getStudentId() + ", 创建时间=" + duplicate.getCreateTime());
            }
        }
        
    } catch (Exception e) {
        System.err.println("清理重复数据失败: " + e.getMessage());
    }
}
```

### 3. 改进测试数据创建逻辑
在创建测试学生数据时，添加了重复检查：
```java
// 检查学生是否已存在
LambdaQueryWrapper<EduStudent> checkWrapper = new LambdaQueryWrapper<>();
checkWrapper.eq(EduStudent::getStudentNo, parts[0]);
checkWrapper.eq(EduStudent::getDelFlag, "0");
EduStudent existingStudent = getOne(checkWrapper);

if (existingStudent != null) {
    System.out.println("学生 " + parts[0] + " 已存在，跳过创建");
    continue;
}
```

## 修改的文件
1. `student-points/src/main/java/com/zhentao/config/SecurityConfig.java`
2. `student-points/src/main/java/com/zhentao/service/impl/EduStudentServiceImpl.java`

## 测试建议
1. 重新启动应用程序
2. 使用文书账号 `sunshanshan` 登录
3. 验证登录是否成功，不再出现 `TooManyResultsException` 错误
4. 测试学生搜索功能是否正常工作

## 预期结果
- 用户登录成功，不再出现重复记录错误
- 文书用户可以正常搜索本班级学生
- 系统会自动清理重复的学生记录
- 避免创建重复的测试数据
