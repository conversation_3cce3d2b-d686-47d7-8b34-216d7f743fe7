# 修复重复学生记录导致的登录错误

## 错误描述
```
org.apache.ibatis.exceptions.TooManyResultsException: Expected one result (or null) to be returned by selectOne(), but found: 2
```

## 错误原因分析
1. **错误位置**: `SecurityConfig.java:139` 行的 `onAuthenticationSuccess` 方法
2. **错误原因**: 在用户登录成功后，系统尝试通过手机号和姓名查找学生记录时，发现了多个匹配的记录
3. **根本原因**: 我们之前的修改中自动创建了文书学生记录，可能导致数据库中存在重复的学生数据

## 问题代码
```java
// SecurityConfig.java 第139行
EduStudent student = eduStudentService.getOne(new QueryWrapper<EduStudent>()
        .eq("phone", sysUser.getPhone())
        .eq("real_name", sysUser.getRealName())
);
```

当数据库中有多个学生具有相同的手机号和姓名时，`getOne()` 方法会抛出 `TooManyResultsException`。

## 解决方案

### 1. 修改 SecurityConfig.java 中的查询逻辑
```java
EduStudent student = null;
try {
    // 首先尝试通过用户名（学号）查找
    student = eduStudentService.getOne(new QueryWrapper<EduStudent>()
            .eq("student_no", sysUser.getUsername())
            .eq("del_flag", "0")
            .last("LIMIT 1")
    );
    
    // 如果通过学号找不到，再尝试通过手机号和姓名查找
    if (student == null && sysUser.getPhone() != null && sysUser.getRealName() != null) {
        List<EduStudent> students = eduStudentService.list(new QueryWrapper<EduStudent>()
                .eq("phone", sysUser.getPhone())
                .eq("real_name", sysUser.getRealName())
                .eq("del_flag", "0")
        );
        if (!students.isEmpty()) {
            student = students.get(0); // 取第一个匹配的学生
            System.out.println("通过手机号和姓名找到学生，取第一个: " + student.getStudentNo());
        }
    }
} catch (Exception e) {
    System.err.println("查找学生信息时出错: " + e.getMessage());
    student = null;
}
```

### 2. 添加重复数据清理功能
在 `EduStudentServiceImpl.java` 中添加了 `cleanupDuplicateStudents` 方法：
```java
private void cleanupDuplicateStudents() {
    try {
        System.out.println("开始清理重复的学生数据...");
        
        // 查找文书用户的重复记录
        LambdaQueryWrapper<EduStudent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EduStudent::getStudentNo, "sunshanshan");
        wrapper.eq(EduStudent::getDelFlag, "0");
        wrapper.orderByDesc(EduStudent::getCreateTime);
        
        List<EduStudent> duplicates = list(wrapper);
        if (duplicates.size() > 1) {
            System.out.println("发现文书用户 sunshanshan 有 " + duplicates.size() + " 条重复记录，保留最新的一条");
            
            // 保留第一条（最新的），删除其他的
            for (int i = 1; i < duplicates.size(); i++) {
                EduStudent duplicate = duplicates.get(i);
                duplicate.setDelFlag(1); // 标记为删除
                updateById(duplicate);
                System.out.println("删除重复记录: ID=" + duplicate.getStudentId() + ", 创建时间=" + duplicate.getCreateTime());
            }
        }
        
    } catch (Exception e) {
        System.err.println("清理重复数据失败: " + e.getMessage());
    }
}
```

### 3. 改进测试数据创建逻辑
在创建测试学生数据时，添加了重复检查：
```java
// 检查学生是否已存在
LambdaQueryWrapper<EduStudent> checkWrapper = new LambdaQueryWrapper<>();
checkWrapper.eq(EduStudent::getStudentNo, parts[0]);
checkWrapper.eq(EduStudent::getDelFlag, "0");
EduStudent existingStudent = getOne(checkWrapper);

if (existingStudent != null) {
    System.out.println("学生 " + parts[0] + " 已存在，跳过创建");
    continue;
}
```

## 修改的文件
1. `student-points/src/main/java/com/zhentao/config/SecurityConfig.java`
2. `student-points/src/main/java/com/zhentao/service/impl/EduStudentServiceImpl.java`

## 测试建议
1. 重新启动应用程序
2. 使用文书账号 `sunshanshan` 登录
3. 验证登录是否成功，不再出现 `TooManyResultsException` 错误
4. 测试学生搜索功能是否正常工作

## 预期结果
- 用户登录成功，不再出现重复记录错误
- 文书用户可以正常搜索本班级学生
- 系统会自动清理重复的学生记录
- 避免创建重复的测试数据

## 新发现的问题和修复

### 问题：NullPointerException - 班级不存在
```
java.lang.NullPointerException: Cannot invoke "com.zhentao.pojo.EduClass.getClassName()" because "byId" is null
```

**原因**: 硬编码的班级ID为1的班级在数据库中不存在。

### 解决方案

#### 1. 修复SecurityConfig中的空指针检查
```java
if (student!=null){
    try {
        EduClass byId = eduClassService.getById(student.getClassId());
        if (byId != null) {
            student.setClassName(byId.getClassName());
            if (byId.getStageId() != null) {
                EduStage byId1 = eduStageService.getById(byId.getStageId());
                if (byId1 != null) {
                    student.setStageName(byId1.getStageName());
                }
            }
        } else {
            System.err.println("班级ID " + student.getClassId() + " 不存在");
            student.setClassName("未知班级");
            student.setStageName("未知专业");
        }
    } catch (Exception e) {
        System.err.println("获取班级信息失败: " + e.getMessage());
        student.setClassName("未知班级");
        student.setStageName("未知专业");
    }
}
```

#### 2. 动态获取可用班级ID
添加了 `getAvailableClassId()` 方法：
```java
private Integer getAvailableClassId() {
    try {
        // 查找第一个可用的班级
        LambdaQueryWrapper<EduClass> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EduClass::getDelFlag, 0);
        wrapper.eq(EduClass::getStatus, 0);
        wrapper.last("LIMIT 1");

        EduClass availableClass = eduClassService.getOne(wrapper);
        if (availableClass != null) {
            System.out.println("找到可用班级: " + availableClass.getClassId() + " - " + availableClass.getClassName());
            return availableClass.getClassId();
        }

        System.err.println("没有找到可用的班级");
        return null;
    } catch (Exception e) {
        System.err.println("获取可用班级ID失败: " + e.getMessage());
        return null;
    }
}
```

#### 3. 改进测试数据创建
使用动态学号前缀，避免硬编码：
```java
// 获取班级信息来生成合适的学号前缀
String studentNoPrefix = "TEST" + String.format("%02d", classId);

// 创建测试学生数据
String[] testStudents = {
    studentNoPrefix + "01,张三,1,13800000001,<EMAIL>,95",
    studentNoPrefix + "02,李四,0,13800000002,<EMAIL>,105",
    // ...
};
```

## 最终修复效果
现在系统应该能够：
1. ✅ 正常处理用户登录，不再出现异常
2. ✅ 自动查找可用的班级ID
3. ✅ 创建合适的学生记录和测试数据
4. ✅ 文书用户可以正常搜索本班级学生
5. ✅ 提供详细的错误处理和日志记录
