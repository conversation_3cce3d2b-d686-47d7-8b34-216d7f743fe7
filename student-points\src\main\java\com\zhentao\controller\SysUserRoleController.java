package com.zhentao.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zhentao.pojo.SysUserRole;
import com.zhentao.service.SysUserRoleService;
import com.zhentao.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 用户和角色关联表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@RestController
@RequestMapping("/sys-user-role")
public class SysUserRoleController {

    @Autowired
    private SysUserRoleService sysUserRoleService;

    // 添加学生权限
    // 跨域注解
    @CrossOrigin
    @PostMapping("/addUserRole")
    public Result addUserRole(@RequestBody Map<String, Object> params) {
        Integer userId = (Integer) params.get("userId");
        List<Integer> roleIds = (List<Integer>) params.get("roleIds");

        try {
            sysUserRoleService.remove(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        for (Integer roleId : roleIds) {
            SysUserRole sysUserRole = new SysUserRole();
            sysUserRole.setUserId(userId);
            sysUserRole.setRoleId(roleId);
            sysUserRoleService.save(sysUserRole);
        }

        return Result.OK();
    }

}
