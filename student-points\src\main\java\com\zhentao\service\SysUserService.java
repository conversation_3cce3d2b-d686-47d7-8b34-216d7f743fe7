package com.zhentao.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zhentao.dto.system.system.PasswordDto;
import com.zhentao.dto.system.system.SysUserDto;
import com.zhentao.pojo.SysUser;
import com.zhentao.utils.Result;

/**
 * <p>
 * 系统用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
public interface SysUserService extends IService<SysUser> {
    /**
     * 分页查询用户
     * @param sysUserDto
     * @return
     */
    public Page<SysUser> getUserPage(SysUserDto sysUserDto);
    /**
     * 添加用户与角色
     */
    public Result addUserRole(SysUser sysUser);
    /**
     * 编辑用户
     */
    public Result updateUserRole(SysUser sysUser);
    /**
     * 删除用户与角色
     */
    public Result deleteUserRole(Integer userId);
    /**
     * 修改用户状态
     */
    public Result updateUserStatus(Integer userId,Integer status);
    /**
     * 重置用户密码
     */
    public Result resetUserPassword(Integer userId);
    /**
     * 修改个人信息
     */
    public Result updateUserInfo(SysUser sysUser);
    /**
     * 修改个人密码
     */
    public Result updateUserPassword(PasswordDto passwordDto);
    /**
     * 查找用户详情
     */
    public Result getUserDetail();

    Result JSList0();
}
