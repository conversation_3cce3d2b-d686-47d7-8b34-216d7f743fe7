import request from '@/utils/request'

/**
 * 批量导入相关API
 */

// 下载学生信息导入模板
export function downloadStudentTemplate() {
  return request({
    url: '/edu-student/template/download',
    method: 'get',
    responseType: 'blob'
  })
}

// 批量导入学生信息 - 高性能优化版本
export function batchImportStudents(data) {
  return request({
    url: '/edu-student/batch/import',
    method: 'post',
    data: data,
    timeout: 300000, // 5分钟超时，因为批量导入可能需要较长时间
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 验证导入数据
export function validateImportData(data) {
  return request({
    url: '/edu-student/batch/validate',
    method: 'post',
    data: data
  })
}

// 获取导入历史记录
export function getImportHistory(params) {
  return request({
    url: '/edu-student/batch/history',
    method: 'get',
    params: params
  })
}

// 获取导入结果详情
export function getImportResult(importId) {
  return request({
    url: `/edu-student/batch/result/${importId}`,
    method: 'get'
  })
}

// 归档相关API

// 设置自动归档时间
export function setArchiveTime(archiveTimeStr) {
  return request({
    url: '/edu-student/setArchiveTime',
    method: 'post',
    params: { archiveTimeStr }
  })
}

// 取消自动归档
export function cancelArchive() {
  return request({
    url: '/edu-student/cancelArchive',
    method: 'post'
  })
}

// 获取归档状态
export function getArchiveStatus() {
  return request({
    url: '/edu-student/getArchiveStatus',
    method: 'get'
  })
}

// 测试归档功能
export function testArchiveFunction() {
  return request({
    url: '/edu-student/testArchive',
    method: 'post'
  })
}
