-- 云计算学院积分管理系统简化迁移脚本
-- 执行日期：2025-08-07
-- 说明：只创建必要的表和数据，不修改现有表结构

-- =====================================================
-- 1. 创建文书积分申请表
-- =====================================================

-- 删除表（如果存在）
DROP TABLE IF EXISTS `cultural_secretary_apply`;

-- 创建文书积分申请表
CREATE TABLE `cultural_secretary_apply` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `student_no` varchar(50) NOT NULL COMMENT '学生学号',
  `class_id` int NOT NULL COMMENT '班级Id',
  `apply_user_id` int NOT NULL COMMENT '申请人Id',
  `points_change` int NOT NULL COMMENT '变动状态（1-加分，2-减分）',
  `points` int NOT NULL COMMENT '分值',
  `reason` text COMMENT '申请理由',
  `evidence_images` text COMMENT '证明图片URL，多个以逗号分隔',
  `status` int DEFAULT '1' COMMENT '审核状态（1-待审核，2-已通过，3-已拒绝，4-已撤销）',
  `reviewer_id` int DEFAULT NULL COMMENT '审核人ID',
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `review_comment` text COMMENT '审核意见',
  `create_by` int DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` int DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` text COMMENT '备注',
  `del_flag` int DEFAULT '1' COMMENT '删除标志（1-存在，2-删除）',
  PRIMARY KEY (`id`),
  KEY `idx_student_no` (`student_no`),
  KEY `idx_class_id` (`class_id`),
  KEY `idx_apply_user_id` (`apply_user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文书积分申请表';

-- =====================================================
-- 2. 添加文书角色
-- =====================================================

-- 插入文书角色
INSERT IGNORE INTO `sys_role` (`role_id`, `role_name`, `role_key`, `role_sort`, `status`, `create_by`, `create_time`, `remark`, `del_flag`) 
VALUES (8, '文书', 'cultural_secretary', 8, 0, 1, NOW(), '班级文化书记，负责班级积分申请', 0);

-- =====================================================
-- 3. 创建测试用户
-- =====================================================

-- 查看现有学生（用于选择文书）
SELECT student_no, student_name, class_id FROM edu_student WHERE del_flag = '0' LIMIT 5;

-- 创建文书用户（请根据实际学生学号修改username）
-- 注意：username必须是现有学生的student_no
INSERT IGNORE INTO `sys_user` (
  `username`, `password`, `real_name`, `gender`, `phone`, `email`, 
  `user_type`, `status`, `create_by`, `create_time`, `del_flag`
) VALUES (
  '2023001001', '$2a$10$7JB720yubVSOfvVaMWye2.bpe1HpoVMpTGYTP2K4OqHDqukjojvi.', -- 密码: 123456
  '张文书', 1, '13800000001', '<EMAIL>', 
  8, 0, 1, NOW(), 0
);

-- 为文书用户分配角色
INSERT IGNORE INTO `sys_user_role` (`user_id`, `role_id`) 
SELECT u.`user_id`, 8 
FROM `sys_user` u 
WHERE u.`username` = '2023001001' AND u.`user_type` = 8;

-- 创建秘书用户
INSERT IGNORE INTO `sys_user` (
  `username`, `password`, `real_name`, `gender`, `phone`, `email`, 
  `user_type`, `status`, `create_by`, `create_time`, `del_flag`
) VALUES (
  'mishu001', '$2a$10$7JB720yubVSOfvVaMWye2.bpe1HpoVMpTGYTP2K4OqHDqukjojvi.', -- 密码: 123456
  '李秘书', 0, '13800000002', '<EMAIL>', 
  6, 0, 1, NOW(), 0
);

-- 为秘书用户分配角色
INSERT IGNORE INTO `sys_user_role` (`user_id`, `role_id`) 
SELECT u.`user_id`, 6 
FROM `sys_user` u 
WHERE u.`username` = 'mishu001' AND u.`user_type` = 6;

-- =====================================================
-- 4. 验证数据
-- =====================================================

-- 检查文书积分申请表
DESCRIBE `cultural_secretary_apply`;

-- 检查角色
SELECT * FROM `sys_role` WHERE `role_id` IN (6, 8);

-- 检查用户和班级关联
SELECT u.`username`, u.`real_name`, u.`user_type`, r.`role_name`,
       es.`class_id`, ec.`class_name`
FROM `sys_user` u
LEFT JOIN `sys_user_role` ur ON u.`user_id` = ur.`user_id`
LEFT JOIN `sys_role` r ON ur.`role_id` = r.`role_id`
LEFT JOIN `edu_student` es ON u.`username` = es.`student_no`
LEFT JOIN `edu_class` ec ON es.`class_id` = ec.`class_id`
WHERE u.`user_type` IN (6, 8)
ORDER BY u.`user_type`, u.`username`;

-- =====================================================
-- 5. 使用说明
-- =====================================================

/*
使用说明：

1. 文书功能逻辑：
   - 文书用户的username必须对应学生表中的student_no
   - 系统通过学生表的class_id字段获取文书所在班级
   - 文书只能为同班级学生申请积分

2. 测试步骤：
   - 使用文书账号登录：username=2023001001, password=123456
   - 访问测试接口：GET /cultural-secretary-apply/test/secretary-info
   - 查看文书信息和班级信息
   - 提交积分申请：POST /cultural-secretary-apply/submit
   - 使用秘书账号审核：username=mishu001, password=123456

3. 注意事项：
   - 确保文书用户名对应的学生记录存在
   - 确保学生记录中的class_id有效
   - 文书只能为本班学生申请积分
*/

SELECT '数据库迁移脚本执行完成！' AS message;
SELECT '请重启应用服务器，然后使用测试账号验证功能。' AS notice;
