<template>
  <div class="batch-import-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>批量导入积分申请</h2>
      <p>通过Excel文件批量导入学生积分申请，提高工作效率</p>
    </div>

    <!-- 返回按钮 -->
    <div class="back-section">
      <el-button @click="goBack" type="info" plain>
        <el-icon><ArrowLeft /></el-icon>
        返回申请管理
      </el-button>
    </div>

    <!-- Excel批量导入区域 -->
    <el-card class="import-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-icon><Upload /></el-icon>
          <span>批量导入申请</span>
        </div>
      </template>

      <!-- 操作步骤指示器 -->
      <div class="steps-container">
        <el-steps :active="currentStep" finish-status="success" align-center>
          <el-step title="下载模板" description="获取Excel导入模板（可选）"></el-step>
          <el-step title="填写数据" description="在模板中填写学生申请信息"></el-step>
          <el-step title="上传文件" description="选择并上传填写好的Excel文件"></el-step>
          <el-step title="验证数据" description="系统验证数据格式和内容"></el-step>
          <el-step title="确认提交" description="确认无误后批量提交申请"></el-step>
        </el-steps>
      </div>

      <!-- 操作按钮区 -->
      <div class="import-actions">
        <div class="action-group">
          <el-button
            type="primary"
            @click="downloadTemplate"
            :loading="downloadingTemplate"
            size="large"
          >
            <el-icon><Download /></el-icon>
            下载导入模板
          </el-button>
          <span class="action-tip">可选：下载标准Excel模板（如果您已有模板可跳过）</span>
        </div>

        <div class="action-group">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :show-file-list="false"
            accept=".xlsx,.xls"
            @change="handleExcelFileChange"
          >
            <el-button
              type="success"
              size="large"
            >
              <el-icon><Upload /></el-icon>
              选择Excel文件
            </el-button>
          </el-upload>
          <span class="action-tip">选择填写好的Excel文件（支持.xlsx和.xls格式）</span>
        </div>

        <div class="action-group">
          <el-button
            type="warning"
            :disabled="!selectedExcelFile"
            @click="processImport"
            :loading="importing"
            size="large"
          >
            <el-icon><Search /></el-icon>
            验证并解析数据
          </el-button>
          <span class="action-tip">第三步：验证数据格式和内容</span>
        </div>

        <div class="action-group" v-if="importResult && importResult.details && importResult.details.length > 0">
          <el-button
            type="danger"
            :disabled="!hasValidData"
            @click="batchSubmit"
            :loading="batchSubmitting"
            size="large"
          >
            <el-icon><Check /></el-icon>
            批量提交申请
          </el-button>
          <span class="action-tip">第四步：确认提交所有有效申请</span>
        </div>

        <div class="action-group">
          <el-button @click="resetImport" plain>
            <el-icon><Refresh /></el-icon>
            重新开始
          </el-button>
        </div>
      </div>

      <!-- 文件信息显示 -->
      <div v-if="selectedExcelFile" class="file-info">
        <el-card shadow="never" class="file-info-card">
          <template #header>
            <div class="file-header">
              <el-icon><Document /></el-icon>
              <span>已选择的文件</span>
            </div>
          </template>
          <div class="file-details">
            <div class="file-item">
              <span class="label">文件名:</span>
              <span class="value">{{ selectedExcelFile.name }}</span>
            </div>
            <div class="file-item">
              <span class="label">文件大小:</span>
              <span class="value">{{ formatFileSize(selectedExcelFile.size) }}</span>
            </div>
            <div class="file-item">
              <span class="label">文件类型:</span>
              <span class="value">{{ selectedExcelFile.type || 'Excel文件' }}</span>
            </div>
            <div class="file-item">
              <span class="label">上传时间:</span>
              <span class="value">{{ new Date().toLocaleString() }}</span>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 导入结果展示 -->
      <div v-if="importResult && importResult.details && importResult.details.length > 0" class="import-result">
        <!-- 统计信息 -->
        <div class="result-summary">
          <div class="summary-header">
            <h3>导入结果统计</h3>
            <el-tag :type="importResult.failCount > 0 ? 'warning' : 'success'" size="large">
              {{ importResult.failCount > 0 ? '部分成功' : '全部成功' }}
            </el-tag>
          </div>

          <el-row :gutter="20" class="statistics-row">
            <el-col :span="6">
              <div class="stat-card total">
                <div class="stat-icon">📊</div>
                <div class="stat-content">
                  <div class="stat-value">{{ importResult.totalCount }}</div>
                  <div class="stat-label">总记录数</div>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card success">
                <div class="stat-icon">✅</div>
                <div class="stat-content">
                  <div class="stat-value">{{ importResult.successCount }}</div>
                  <div class="stat-label">成功记录</div>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card error">
                <div class="stat-icon">❌</div>
                <div class="stat-content">
                  <div class="stat-value">{{ importResult.failCount }}</div>
                  <div class="stat-label">失败记录</div>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card time">
                <div class="stat-icon">⏱️</div>
                <div class="stat-content">
                  <div class="stat-value">{{ importResult.processTime }}ms</div>
                  <div class="stat-label">处理耗时</div>
                </div>
              </div>
            </el-col>
          </el-row>

          <!-- 成功率进度条 -->
          <div class="progress-section">
            <div class="progress-label">
              <span>成功率</span>
              <span>{{ Math.round((importResult.successCount / importResult.totalCount) * 100) }}%</span>
            </div>
            <el-progress
              :percentage="Math.round((importResult.successCount / importResult.totalCount) * 100)"
              :color="importResult.failCount > 0 ? '#e6a23c' : '#67c23a'"
              :stroke-width="8"
            />
          </div>
        </div>

        <!-- 错误汇总 -->
        <div v-if="importResult.errorSummary && importResult.errorSummary.length > 0" class="error-summary">
          <el-alert
            title="导入过程中发现以下问题："
            type="warning"
            :closable="false"
            style="margin-top: 15px;"
          >
            <ul>
              <li v-for="error in importResult.errorSummary" :key="error">{{ error }}</li>
            </ul>
          </el-alert>
        </div>

        <!-- 详细结果表格 -->
        <div class="result-table" style="margin-top: 20px;">
          <el-table :data="importResult.details" stripe max-height="400">
            <el-table-column prop="rowIndex" label="行号" width="80" />
            <el-table-column prop="studentNo" label="学号" width="120" />
            <el-table-column prop="studentName" label="姓名" width="100" />
            <el-table-column prop="className" label="班级" width="150" />
            <el-table-column prop="pointsChangeText" label="类型" width="80" />
            <el-table-column prop="points" label="分值" width="80" />
            <el-table-column prop="reason" label="申请原因" min-width="200" show-overflow-tooltip />
            <el-table-column prop="isValid" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.isValid ? 'success' : 'danger'">
                  {{ scope.row.isValid ? '验证通过' : '验证失败' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="errorMessage" label="错误信息" min-width="200" show-overflow-tooltip>
              <template #default="scope">
                <span v-if="!scope.row.isValid" style="color: #f56c6c;">{{ scope.row.errorMessage }}</span>
                <span v-else style="color: #67c23a;">-</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>

    <!-- 使用说明 -->
    <el-card class="help-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-icon><QuestionFilled /></el-icon>
          <span>使用说明</span>
        </div>
      </template>
      
      <div class="help-content">
        <h4>操作步骤：</h4>
        <ol>
          <li>（可选）点击"下载导入模板"获取Excel模板文件</li>
          <li>在Excel中填写学生积分申请信息</li>
          <li>点击"选择Excel文件"上传填写好的文件</li>
          <li>点击"开始导入"解析和验证数据</li>
          <li>检查导入结果，确认无误后点击"批量提交"</li>
        </ol>

        <h4>Excel格式要求：</h4>
        <ul>
          <li><strong>文件格式</strong>：支持.xlsx和.xls格式</li>
          <li><strong>文件大小</strong>：最大10MB</li>
          <li><strong>表头格式</strong>：第一行为字段名，第二行为说明，第三行为示例（自动跳过），第四行开始为实际数据</li>
          <li><strong>必填字段</strong>：学号、姓名、班级、申请类型、分值、申请原因</li>
          <li><strong>可选字段</strong>：证明素材（填写证明材料的说明文字）</li>
          <li><strong>示例行处理</strong>：学号以"示例："开头的行会被自动跳过，不会导入</li>
        </ul>

        <h4>字段说明：</h4>
        <ul>
          <li><strong>学号</strong>：必须是系统中存在的学生学号</li>
          <li><strong>姓名</strong>：必须与学号对应的学生姓名一致</li>
          <li><strong>班级</strong>：必须是系统中存在的班级名称</li>
          <li><strong>申请类型</strong>：只能填写"加分"或"减分"</li>
          <li><strong>分值</strong>：加分1-100分，减分1-50分</li>
          <li><strong>申请原因</strong>：不能为空，详细说明申请理由</li>
          <li><strong>证明素材</strong>：可选，填写证明材料的说明（如：获奖证书、比赛照片等）</li>
        </ul>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// 定义emit
const emit = defineEmits(['goBack'])
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Upload, Download, ArrowLeft, QuestionFilled, Search, Check, Refresh, Document
} from '@element-plus/icons-vue'
import {
  downloadPointsApplyTemplate,
  batchImportPointsApply,
  batchSubmitPointsApply
} from '@/api/student/application'

// 组件引用
const uploadRef = ref()

// 状态变量
const downloadingTemplate = ref(false)
const importing = ref(false)
const batchSubmitting = ref(false)
const selectedExcelFile = ref(null)
const importResult = ref(null)
const templateDownloaded = ref(false)
const currentStep = ref(0)

// 计算属性：是否有有效数据可以提交
const hasValidData = computed(() => {
  return importResult.value && 
         importResult.value.details && 
         importResult.value.details.some(item => item.isValid)
})

// 返回上一页
const goBack = () => {
  // 通知父组件返回申请管理页面
  emit('goBack')
}

// 下载Excel模板
const downloadTemplate = async () => {
  try {
    downloadingTemplate.value = true

    const response = await downloadPointsApplyTemplate()

    // 检查响应数据
    console.log('模板下载响应:', response)

    // 确保响应是Blob类型
    let blob
    if (response.data instanceof Blob) {
      blob = response.data
    } else if (response instanceof Blob) {
      blob = response
    } else {
      // 如果不是Blob，尝试转换
      blob = new Blob([response.data || response], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
    }

    // 检查Blob大小
    if (blob.size === 0) {
      throw new Error('下载的文件为空')
    }

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = '积分申请导入模板.xlsx'
    link.style.display = 'none'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('模板下载成功')
    templateDownloaded.value = true
    currentStep.value = 1
  } catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('下载模板失败: ' + (error.message || '网络错误'))
  } finally {
    downloadingTemplate.value = false
  }
}

// 处理Excel文件选择
const handleExcelFileChange = (file) => {
  selectedExcelFile.value = file.raw
  importResult.value = null
  // 如果还没下载过模板，直接跳到步骤2，否则继续当前步骤
  if (currentStep.value < 2) {
    currentStep.value = 2
  }
  console.log('选择的文件:', file.raw)
  ElMessage.success(`已选择文件: ${file.raw.name}`)
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 处理Excel导入
const processImport = async () => {
  if (!selectedExcelFile.value) {
    ElMessage.warning('请先选择Excel文件')
    return
  }

  try {
    importing.value = true

    console.log('开始导入文件:', selectedExcelFile.value.name)
    const result = await batchImportPointsApply(selectedExcelFile.value)
    console.log('导入API响应:', result)
    console.log('响应数据结构:', result.data)

    // 处理响应数据结构
    const responseData = result.data || result
    console.log('处理后的响应数据:', responseData)

    if (responseData.code === 200) {
      importResult.value = responseData.data
      currentStep.value = 3
      console.log('导入结果数据:', responseData.data)
      ElMessage.success(`导入解析完成，共${responseData.data.totalCount}条记录，成功${responseData.data.successCount}条，失败${responseData.data.failCount}条`)
    } else {
      console.error('导入失败，响应码:', responseData.code, '消息:', responseData.message)
      ElMessage.error('导入失败: ' + (responseData.message || '未知错误'))
    }
  } catch (error) {
    console.error('导入异常:', error)
    console.error('异常详情:', {
      message: error.message,
      response: error.response,
      stack: error.stack
    })
    ElMessage.error('导入失败: ' + (error.message || '网络错误'))
  } finally {
    importing.value = false
  }
}

// 批量提交
const batchSubmit = async () => {
  if (!importResult.value || !importResult.value.details) {
    ElMessage.warning('没有可提交的数据')
    return
  }
  
  // 只提交验证通过的数据
  const validData = importResult.value.details.filter(item => item.isValid)
  
  if (validData.length === 0) {
    ElMessage.warning('没有验证通过的数据可以提交')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要批量提交${validData.length}条积分申请吗？`,
      '确认提交',
      {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }
    )

    batchSubmitting.value = true

    console.log('开始批量提交:', validData)
    const result = await batchSubmitPointsApply(validData)
    console.log('批量提交API响应:', result)

    // 处理响应数据结构
    const responseData = result.data || result
    console.log('处理后的提交响应数据:', responseData)

    if (responseData.code === 200) {
      ElMessage.success(responseData.message || '批量提交成功')
      currentStep.value = 4
      // 清空导入结果
      setTimeout(() => {
        resetImport()
      }, 2000)
    } else {
      console.error('批量提交失败，响应码:', responseData.code, '消息:', responseData.message)
      ElMessage.error('批量提交失败: ' + (responseData.message || '未知错误'))
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量提交异常:', error)
      console.error('异常详情:', {
        message: error.message,
        response: error.response,
        stack: error.stack
      })
      ElMessage.error('批量提交失败: ' + (error.message || '网络错误'))
    }
  } finally {
    batchSubmitting.value = false
  }
}

// 重置导入流程
const resetImport = () => {
  selectedExcelFile.value = null
  importResult.value = null
  templateDownloaded.value = false
  currentStep.value = 0

  // 清空上传组件
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }

  ElMessage.info('已重置，可以重新开始导入流程')
}
</script>

<style scoped>
.batch-import-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h2 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.page-header p {
  color: #7f8c8d;
  font-size: 14px;
  margin: 0;
}

.back-section {
  margin-bottom: 20px;
}

.import-card, .help-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: none;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #2c3e50;
}

.steps-container {
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
}

.import-actions {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 20px;
}

.action-group {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.action-tip {
  color: #666;
  font-size: 14px;
  font-style: italic;
}

.file-info {
  margin-top: 20px;
}

.file-info-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.file-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #409eff;
}

.file-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item .label {
  font-weight: 500;
  color: #666;
}

.file-item .value {
  color: #333;
  font-weight: 600;
}

.import-result {
  margin-top: 20px;
}

.result-summary {
  padding: 25px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
  margin-bottom: 20px;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.summary-header h3 {
  margin: 0;
  color: white;
  font-size: 20px;
}

.statistics-row {
  margin-bottom: 20px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.progress-section {
  background: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.progress-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-weight: 500;
}

.error-summary {
  margin-top: 15px;
}

.error-summary ul {
  margin: 10px 0 0 0;
  padding-left: 20px;
}

.error-summary li {
  margin-bottom: 5px;
  color: #e6a23c;
}

.result-table {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

.help-content h4 {
  color: #2c3e50;
  margin-top: 20px;
  margin-bottom: 10px;
}

.help-content ol, .help-content ul {
  padding-left: 20px;
}

.help-content li {
  margin-bottom: 8px;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .batch-import-page {
    padding: 10px;
  }
  
  .import-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .import-actions .el-button {
    width: 100%;
    margin-left: 0 !important;
    margin-bottom: 10px;
  }
}
</style>
