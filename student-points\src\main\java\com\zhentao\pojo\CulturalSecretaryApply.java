package com.zhentao.pojo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-07
 */
@Getter
@Setter
@TableName("cultural_secretary_apply")
public class CulturalSecretaryApply implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键Id
     */
    private Integer id;

    /**
     * 学生学号
     */
    private String studentNo;

    /**
     * 班级Id
     */
    private Integer classId;

    /**
     * 申请人Id
     */
    private Integer applyUserId;

    /**
     * 变动状态（1-加分，2-减分）
     */
    private Integer pointsChange;

    /**
     * 分值
     */
    private Integer points;

    /**
     * 申请理由
     */
    private String reason;

    /**
     * 证明图片URL，多个以逗号分隔
     */
    private String evidenceImages;

    /**
     * 秘书部审核状态（4-已撤销，1-待审核，2-已通过，3-已拒绝）
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 审核人ID
     */
    private Integer reviewerId;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reviewTime;

    /**
     * 审核意见
     */
    private String reviewComment;

    /**
     * 创建者ID
     */
    private Integer createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者ID
     */
    private Integer updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（1-存在，2-删除）
     */
    private Integer delFlag;
}
