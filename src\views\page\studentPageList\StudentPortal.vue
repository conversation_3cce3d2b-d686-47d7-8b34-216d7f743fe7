<template>
  <div class="student-portal">
    <!-- 顶部导航栏 -->
    <el-header class="portal-header">
      <div class="header-content">
        <div class="logo-section">
          <el-icon class="logo-icon"><School /></el-icon>
          <h2>学生服务门户</h2>
        </div>
        <el-menu
            :default-active="activeTab"
            class="header-menu"
            mode="horizontal"
            @select="handleTabSelect"
        >
          <el-menu-item index="home">
            <el-icon><House /></el-icon>
            <span>首页</span>
          </el-menu-item>
          <el-menu-item index="student-info">
            <el-icon><User /></el-icon>
            <span>学生明细</span>
          </el-menu-item>
          <el-menu-item index="application" v-show="currentUser.role===2">
            <el-icon><Document /></el-icon>
            <span>申请管理</span>
          </el-menu-item>
          <el-menu-item index="about">
            <el-icon><InfoFilled /></el-icon>
            <span>关于我们</span>
          </el-menu-item>
        </el-menu>
        <div class="user-info">
          <el-dropdown>
            <span class="user-name">
              <el-icon><Avatar /></el-icon>
              {{ currentUser.name }}
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>个人设置</el-dropdown-item>
                <el-dropdown-item @click="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </el-header>

    <!-- 主要内容区域 -->
    <el-main class="portal-main">
      <!-- 首页模块 -->
      <div v-show="activeTab === 'home'" class="tab-content">
        <HomePage />
      </div>

      <!-- 学生明细模块 -->
      <div v-show="activeTab === 'student-info'" class="tab-content">
        <StudentInfoPage />
      </div>

      <!-- 申请管理模块 -->
      <div v-show="activeTab === 'application'" class="tab-content">
        <ApplicationPage @switchToImport="switchToBatchImport" />
      </div>

      <!-- 批量导入模块 -->
      <div v-show="activeTab === 'batch-import'" class="tab-content">
        <BatchImportPage @goBack="goBackToApplication" />
      </div>

      <!-- 关于我们模块 -->
      <div v-show="activeTab === 'about'" class="tab-content">
        <AboutPage />
      </div>
    </el-main>

    <!-- 底部信息 -->
    <el-footer class="portal-footer">
      <div class="footer-content">
        <p>&copy; 2025 学生积分管理系统 - 为学生服务，让管理更简单</p>
      </div>
    </el-footer>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import {
  School, House, User, Document, InfoFilled, Avatar
} from '@element-plus/icons-vue'
import HomePage from './components/HomePage.vue'
import StudentInfoPage from './components/StudentInfoPage.vue'
import ApplicationPage from './components/ApplicationPage.vue'
import BatchImportPage from './components/BatchImportPage.vue'
import AboutPage from './components/AboutPage.vue'

// 当前激活的标签页
const activeTab = ref('home')
var parse = JSON.parse(localStorage.getItem("studentList"));

// 当前用户信息
const currentUser = reactive({
  name: parse.realName,
  studentNo: parse.studentNo,
  className: parse.className,
  role: parse.role
})

// 处理标签页切换
const handleTabSelect = (key) => {
  activeTab.value = key
}

// 切换到批量导入页面
const switchToBatchImport = () => {
  activeTab.value = 'batch-import'
}

// 从批量导入页面返回申请管理页面
const goBackToApplication = () => {
  activeTab.value = 'application'
}

// 退出登录
const logout = () => {
  // 清空当前用户信息
  localStorage.clear()

  // 重定向到登录页面
  window.location.href = '/login';
}
</script>

<style scoped>
.student-portal {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

/* 头部样式 */
.portal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0;
  height: 70px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  font-size: 32px;
  color: #fff;
}

.logo-section h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #fff;
}

.header-menu {
  flex: 1;
  margin: 0 40px;
  background: transparent;
  border: none;
}

.header-menu :deep(.el-menu-item) {
  color: rgba(255, 255, 255, 0.8);
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
}

.header-menu :deep(.el-menu-item:hover),
.header-menu :deep(.el-menu-item.is-active) {
  color: #fff;
  background: rgba(255, 255, 255, 0.1);
  border-bottom-color: #fff;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-name {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background 0.3s;
}

.user-name:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 主要内容区域 */
.portal-main {
  flex: 1;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.tab-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 底部样式 */
.portal-footer {
  background: #2c3e50;
  color: #ecf0f1;
  text-align: center;
  padding: 20px;
  height: auto;
}

.footer-content p {
  margin: 0;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    height: auto;
    padding: 10px;
  }

  .header-menu {
    margin: 10px 0;
    width: 100%;
  }

  .portal-main {
    padding: 10px;
  }
}
</style>
