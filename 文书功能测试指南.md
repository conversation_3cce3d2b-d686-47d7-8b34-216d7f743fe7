# 文书积分申请功能测试指南

## 1. 功能概述

文书积分申请功能允许班级文书为本班学生申请积分加减分，经过秘书部审核后生效。

### 核心逻辑
- **文书识别**：通过用户名(username)关联学生表的学号(student_no)
- **班级权限**：通过学生表的班级ID(class_id)确定文书所在班级
- **权限控制**：文书只能为同班级学生申请积分
- **审核流程**：文书申请 → 秘书审核 → 积分生效

## 2. 数据库准备

### 2.1 执行迁移脚本
```sql
-- 执行简化迁移脚本
source simple_migration.sql;
```

### 2.2 检查现有学生数据
```sql
-- 查看现有学生，选择一个作为文书
SELECT student_no, student_name, class_id, class_name 
FROM edu_student es
LEFT JOIN edu_class ec ON es.class_id = ec.class_id
WHERE es.del_flag = '0' 
LIMIT 10;
```

### 2.3 创建文书用户
```sql
-- 假设选择学号'2023001001'的学生作为文书
-- 如果该学生不存在，先创建学生记录
INSERT IGNORE INTO `edu_student` (
  `student_no`, `student_name`, `gender`, `phone`, `email`, `class_id`, 
  `status`, `create_by`, `create_time`, `del_flag`
) VALUES (
  '2023001001', '张文书', '1', '13800000001', '<EMAIL>', 1,
  '0', 1, NOW(), '0'
);

-- 创建文书用户（username = student_no）
INSERT IGNORE INTO `sys_user` (
  `username`, `password`, `real_name`, `gender`, `phone`, `email`, 
  `user_type`, `status`, `create_by`, `create_time`, `del_flag`
) VALUES (
  '2023001001', '$2a$10$7JB720yubVSOfvVaMWye2.bpe1HpoVMpTGYTP2K4OqHDqukjojvi.', 
  '张文书', 1, '13800000001', '<EMAIL>', 
  8, 0, 1, NOW(), 0
);

-- 分配文书角色
INSERT IGNORE INTO `sys_user_role` (`user_id`, `role_id`) 
SELECT `user_id`, 8 FROM `sys_user` WHERE `username` = '2023001001';
```

## 3. 测试步骤

### 3.1 登录测试
1. **文书登录**
   - 用户名：`2023001001`
   - 密码：`123456`
   - 角色：文书(8)

2. **秘书登录**
   - 用户名：`mishu001`
   - 密码：`123456`
   - 角色：秘书(6)

### 3.2 文书功能测试

#### 3.2.1 获取文书信息
```http
GET /cultural-secretary-apply/test/secretary-info
Authorization: Bearer {token}
```

**预期响应**：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "userId": 123,
    "username": "2023001001",
    "realName": "张文书",
    "userType": 8,
    "studentInfo": {
      "studentNo": "2023001001",
      "studentName": "张文书",
      "classId": 1
    },
    "classInfo": {
      "classId": 1,
      "className": "云计算2301班"
    },
    "classmates": [
      {
        "studentNo": "2023001001",
        "studentName": "张文书",
        "classId": 1
      },
      {
        "studentNo": "2023001002",
        "studentName": "李四",
        "classId": 1
      }
    ]
  }
}
```

#### 3.2.2 获取班级学生列表
```http
GET /cultural-secretary-apply/students/{classId}
Authorization: Bearer {token}
```

#### 3.2.3 提交积分申请
```http
POST /cultural-secretary-apply/submit
Authorization: Bearer {token}
Content-Type: application/json

{
  "studentNo": "2023001002",
  "pointsChange": 1,
  "points": 10,
  "reason": "参加班级活动表现优秀",
  "evidenceImages": "image1.jpg,image2.jpg"
}
```

**预期响应**：
```json
{
  "code": 200,
  "message": "申请提交成功",
  "data": {
    "id": 1,
    "studentNo": "2023001002",
    "status": 1,
    "createTime": "2025-08-07 10:30:00"
  }
}
```

#### 3.2.4 查看我的申请
```http
POST /cultural-secretary-apply/myApplications
Authorization: Bearer {token}
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10,
  "status": 1
}
```

### 3.3 秘书功能测试

#### 3.3.1 查看待审核申请
```http
POST /cultural-secretary-apply/pendingApplications
Authorization: Bearer {token}
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10,
  "status": 1
}
```

#### 3.3.2 审核通过
```http
POST /cultural-secretary-apply/approve/1?reviewComment=申请材料齐全，同意加分
Authorization: Bearer {token}
```

#### 3.3.3 审核拒绝
```http
POST /cultural-secretary-apply/reject/1?reviewComment=证明材料不足，拒绝申请
Authorization: Bearer {token}
```

## 4. 权限验证测试

### 4.1 跨班级申请测试
1. 文书尝试为其他班级学生申请积分
2. 预期结果：返回权限不足错误

### 4.2 角色权限测试
1. 非文书用户访问文书接口
2. 非秘书用户访问秘书接口
3. 预期结果：返回权限不足错误

## 5. 数据验证

### 5.1 检查申请记录
```sql
SELECT * FROM cultural_secretary_apply ORDER BY create_time DESC;
```

### 5.2 检查积分变动
```sql
-- 查看学生积分变动
SELECT student_no, points_before, points_after, points_change, operation_time
FROM points_record 
WHERE student_no = '2023001002'
ORDER BY operation_time DESC;

-- 查看学生当前积分
SELECT student_no, student_name, points 
FROM edu_student 
WHERE student_no = '2023001002';
```

### 5.3 检查操作日志
```sql
SELECT * FROM operation_log 
WHERE operation_description LIKE '%文书%' 
ORDER BY operation_time DESC;
```

## 6. 常见问题排查

### 6.1 文书无法找到班级学生
**问题**：文书查询不到班级学生列表
**排查**：
1. 检查文书用户名是否对应学生表中的学号
2. 检查学生记录的class_id是否有效
3. 检查学生记录的del_flag和status字段

```sql
-- 检查文书对应的学生记录
SELECT u.username, u.real_name, es.student_no, es.class_id, ec.class_name
FROM sys_user u
LEFT JOIN edu_student es ON u.username = es.student_no
LEFT JOIN edu_class ec ON es.class_id = ec.class_id
WHERE u.user_type = 8;
```

### 6.2 权限验证失败
**问题**：文书角色验证失败
**排查**：
1. 检查用户角色关联表
2. 检查角色ID是否正确

```sql
-- 检查用户角色关联
SELECT u.username, u.user_type, ur.role_id, r.role_name
FROM sys_user u
LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
LEFT JOIN sys_role r ON ur.role_id = r.role_id
WHERE u.user_type = 8;
```

### 6.3 积分更新失败
**问题**：审核通过后积分未更新
**排查**：
1. 检查学生记录状态
2. 检查积分计算逻辑
3. 查看错误日志

## 7. 测试用例

### 7.1 正常流程测试
- [ ] 文书登录成功
- [ ] 获取班级学生列表
- [ ] 提交加分申请
- [ ] 提交减分申请
- [ ] 秘书审核通过
- [ ] 秘书审核拒绝
- [ ] 积分正确更新

### 7.2 异常流程测试
- [ ] 文书为其他班级学生申请（应失败）
- [ ] 非文书用户访问文书接口（应失败）
- [ ] 非秘书用户审核申请（应失败）
- [ ] 重复审核同一申请（应失败）
- [ ] 申请不存在的学生（应失败）

### 7.3 边界条件测试
- [ ] 积分数值为0（应失败）
- [ ] 积分数值超过100（应失败）
- [ ] 申请理由为空（应失败）
- [ ] 学生已删除状态（应失败）

## 8. 性能测试

### 8.1 并发申请测试
- 多个文书同时提交申请
- 多个秘书同时审核申请

### 8.2 大数据量测试
- 大量申请记录的查询性能
- 分页查询的响应时间

## 9. 总结

通过以上测试步骤，可以全面验证文书积分申请功能的正确性和稳定性。测试过程中如发现问题，请及时记录并反馈给开发团队。
